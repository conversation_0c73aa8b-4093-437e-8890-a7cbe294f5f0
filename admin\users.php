<?php
define('IN_CHAT', true);
require_once '../config.php';

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: ../login.php");
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $user_id = (int)$_POST['user_id'];
    $action = $_POST['action'];
    
    switch ($action) {
        case 'ban':
            $stmt = $pdo->prepare("UPDATE users SET status = 0 WHERE id = ?");
            $stmt->execute([$user_id]);
            break;
            
        case 'unban':
            $stmt = $pdo->prepare("UPDATE users SET status = 1 WHERE id = ?");
            $stmt->execute([$user_id]);
            break;
            
        case 'delete':
            $stmt = $pdo->prepare("DELETE FROM messages WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            break;
            
        case 'ban_ip':
            $stmt = $pdo->prepare("SELECT ip FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $ip = $stmt->fetchColumn();
            
            if ($ip) {
                $stmt = $pdo->prepare("INSERT INTO ip_blacklist (ip, reason) VALUES (?, ?)");
                $stmt->execute([$ip, "Quản trị viên cấm"]);
            }
            break;
            
        case 'update_location':
            $custom_location = trim($_POST['custom_location']);
            $stmt = $pdo->prepare("UPDATE users SET custom_location = ? WHERE id = ?");
            $stmt->execute([$custom_location, $user_id]);
            break;
    }
}

$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

$total_users = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
$total_pages = ceil($total_users / $per_page);

$stmt = $pdo->query("
    SELECT * FROM users 
    ORDER BY created_at DESC 
    LIMIT $per_page OFFSET $offset
");
$users = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý người dùng - Hệ thống chat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a, #1a1a1a);
            color: #00ff00;
            min-height: 100vh;
            font-family: 'Courier New', monospace;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #000000, #111111);
            border-right: 1px solid #00ff00;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        }
        .sidebar .nav-link {
            color: #00ff00;
            padding: 1rem;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(0, 255, 0, 0.1);
        }
        .sidebar .nav-link:hover {
            color: #ffffff;
            background: rgba(0, 255, 0, 0.1);
            box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
        }
        .sidebar .nav-link.active {
            color: #000000;
            background: #00ff00;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        }
        .sidebar .nav-link i {
            margin-right: 10px;
        }
        .main-content {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            margin: 20px;
            padding: 30px;
            border: 1px solid #00ff00;
            box-shadow: 0 0 30px rgba(0, 255, 0, 0.2);
        }
        .user-table {
            background: rgba(0, 0, 0, 0.9);
            border-radius: 15px;
            border: 1px solid #00ff00;
            overflow: hidden;
            box-shadow: 0 0 25px rgba(0, 255, 0, 0.3);
        }
        .table-dark {
            --bs-table-bg: transparent;
            --bs-table-color: #00ff00;
            --bs-table-border-color: rgba(0, 255, 0, 0.3);
        }
        .table-dark th {
            background: rgba(0, 255, 0, 0.1);
            color: #00ff00;
            border-color: rgba(0, 255, 0, 0.3);
        }
        .table-dark td {
            border-color: rgba(0, 255, 0, 0.1);
        }
        .table-dark tbody tr:hover {
            background: rgba(0, 255, 0, 0.05);
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #00ff00;
            object-fit: cover;
        }
        .status-badge {
            padding: 0.5em 1em;
            border-radius: 20px;
            font-size: 0.85em;
            text-transform: uppercase;
            font-weight: bold;
        }
        .btn-group .btn {
            border: 1px solid #00ff00;
            margin: 0 1px;
        }
        .btn-outline-success {
            color: #00ff00;
            border-color: #00ff00;
        }
        .btn-outline-success:hover {
            background: #00ff00;
            color: #000000;
        }
        .btn-outline-warning {
            color: #ffff00;
            border-color: #ffff00;
        }
        .btn-outline-warning:hover {
            background: #ffff00;
            color: #000000;
        }
        .btn-outline-danger {
            color: #ff0000;
            border-color: #ff0000;
        }
        .btn-outline-danger:hover {
            background: #ff0000;
            color: #ffffff;
        }
        .btn-outline-info {
            color: #00ffff;
            border-color: #00ffff;
        }
        .btn-outline-info:hover {
            background: #00ffff;
            color: #000000;
        }
        .btn-outline-light {
            color: #ffffff;
            border-color: #ffffff;
        }
        .btn-outline-light:hover {
            background: #ffffff;
            color: #000000;
        }
        .btn-outline-primary {
            color: #0080ff;
            border-color: #0080ff;
        }
        .btn-outline-primary:hover {
            background: #0080ff;
            color: #ffffff;
        }
        .modal-content {
            background: linear-gradient(135deg, #000000, #111111);
            border: 1px solid #00ff00;
            color: #00ff00;
        }
        .modal-header {
            border-bottom: 1px solid rgba(0, 255, 0, 0.3);
        }
        .form-control {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff00;
            color: #00ff00;
        }
        .form-control:focus {
            background: rgba(0, 0, 0, 0.9);
            border-color: #00ff00;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
            color: #00ff00;
        }
        .pagination .page-link {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff00;
            color: #00ff00;
        }
        .pagination .page-link:hover {
            background: rgba(0, 255, 0, 0.1);
            color: #00ff00;
        }
        .pagination .page-item.active .page-link {
            background: #00ff00;
            color: #000000;
        }
        .admin-title {
            color: #00ff00;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
            font-weight: bold;
        }
        .terminal-text {
            font-family: 'Courier New', monospace;
            color: #00ff00;
            text-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 px-0 sidebar">
                <div class="text-center mb-4 pt-4">
                    <h4 class="admin-title">BẢN ĐIỀU KHIỂN</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt"></i> Tổng quan
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="users.php">
                            <i class="fas fa-users"></i> Quản lý người dùng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="ip_blacklist.php">
                            <i class="fas fa-ban"></i> Danh sách IP cấm
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat.php">
                            <i class="fas fa-comments"></i> Quay lại chat
                        </a>
                    </li>
                </ul>
            </div>

            <div class="col-md-10">
                <div class="main-content">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="terminal-text">QUẢN LÝ NGƯỜI DÙNG</h2>
                        <div class="terminal-text">
                            <i class="far fa-clock"></i> 
                            <?php echo date('Y-m-d H:i:s'); ?>
                        </div>
                    </div>

                    <div class="user-table">
                        <div class="table-responsive">
                            <table class="table table-dark table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Avatar</th>
                                        <th>Tên đăng nhập</th>
                                        <th>Biệt danh</th>
                                        <th>Địa chỉ IP</th>
                                        <th>Trạng thái</th>
                                        <th>Ngày đăng ký</th>
                                        <th>Lần cuối online</th>
                                        <th>Hành động</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td>
                                            <img src="<?php echo $user['avatar'] ? '../' . $user['avatar'] : '../assets/default-avatar.png'; ?>" 
                                                 class="user-avatar">
                                        </td>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                        <td><?php echo htmlspecialchars($user['nickname']); ?></td>
                                        <td><?php echo $user['ip']; ?></td>
                                        <td>
                                            <?php if ($user['status'] == 1): ?>
                                                <span class="status-badge bg-success">Hoạt động</span>
                                            <?php else: ?>
                                                <span class="status-badge bg-danger">Bị cấm</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $user['created_at']; ?></td>
                                        <td><?php echo $user['last_login']; ?></td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="user_messages.php?user_id=<?php echo $user['id']; ?>" 
                                                   class="btn btn-sm btn-outline-info" title="Xem tin nhắn">
                                                    <i class="fas fa-comments"></i>
                                                </a>
                                                <?php if ($user['status'] == 1): ?>
                                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                                            onclick="banUser(<?php echo $user['id']; ?>)" title="Cấm người dùng">
                                                        <i class="fas fa-user-slash"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-outline-success" 
                                                            onclick="unbanUser(<?php echo $user['id']; ?>)" title="Bỏ cấm">
                                                        <i class="fas fa-user-check"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteUser(<?php echo $user['id']; ?>)" title="Xóa người dùng">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-light" 
                                                        onclick="banIP(<?php echo $user['id']; ?>)" title="Cấm IP">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                                        onclick="editLocation(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['custom_location'] ?? ''); ?>')" title="Sửa vị trí">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <?php if ($total_pages > 1): ?>
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="locationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Chỉnh sửa vị trí người dùng</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="locationForm" method="POST">
                        <input type="hidden" name="action" value="update_location">
                        <input type="hidden" name="user_id" id="locationUserId">
                        <div class="mb-3">
                            <label class="form-label">Vị trí tùy chỉnh</label>
                            <input type="text" name="custom_location" id="customLocation" 
                                   class="form-control" placeholder="Ví dụ: Sao Hỏa">
                            <small class="text-muted">Để trống sẽ sử dụng vị trí tự động phát hiện</small>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-success">Lưu thay đổi</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js" integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous"></script>
    <script>
    function banUser(userId) {
        if (confirm('Bạn có chắc muốn cấm người dùng này?')) {
            submitAction(userId, 'ban');
        }
    }

    function unbanUser(userId) {
        if (confirm('Bạn có chắc muốn bỏ cấm người dùng này?')) {
            submitAction(userId, 'unban');
        }
    }

    function deleteUser(userId) {
        if (confirm('Bạn có chắc muốn xóa người dùng này? Hành động này không thể hoàn tác!')) {
            submitAction(userId, 'delete');
        }
    }

    function banIP(userId) {
        if (confirm('Bạn có chắc muốn cấm IP của người dùng này?')) {
            submitAction(userId, 'ban_ip');
        }
    }

    function editLocation(userId, currentLocation) {
        document.getElementById('locationUserId').value = userId;
        document.getElementById('customLocation').value = currentLocation || '';
        new bootstrap.Modal(document.getElementById('locationModal')).show();
    }

    function submitAction(userId, action, extraData = {}) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="user_id" value="${userId}">
            <input type="hidden" name="action" value="${action}">
        `;
        
        for (const [key, value] of Object.entries(extraData)) {
            form.innerHTML += `<input type="hidden" name="${key}" value="${value}">`;
        }
        
        document.body.appendChild(form);
        form.submit();
    }
    </script>
</body>