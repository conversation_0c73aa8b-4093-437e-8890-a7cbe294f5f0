<?php
require_once 'config.php';
$user = requireAuth();

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    jsonResponse(['success' => false, 'message' => 'Không nhận được file ảnh'], 400);
}

$filePath = $_FILES['image']['tmp_name'];
$fileName = $_FILES['image']['name'];

$cloud_name = 'dv6hnveua';
$api_key = '216189142954892';
$api_secret = 'QumSlDqAY7-4FDWF5s9Cp8yu9P4';

$timestamp = time();
$signature = sha1('timestamp=' . $timestamp . $api_secret);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://api.cloudinary.com/v1_1/$cloud_name/image/upload");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, [
    'file' => new CURLFile($filePath, mime_content_type($filePath), $fileName),
    'api_key' => $api_key,
    'timestamp' => $timestamp,
    'signature' => $signature
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code !== 200) {
    jsonResponse(['success' => false, 'message' => 'Lỗi upload lên Cloudinary', 'cloudinary_response' => $response], 500);
}

$data = json_decode($response, true);
if (!isset($data['secure_url'])) {
    jsonResponse(['success' => false, 'message' => 'Không nhận được link ảnh từ Cloudinary', 'cloudinary_response' => $response], 500);
}

jsonResponse([
    'success' => true,
    'url' => $data['secure_url']
]);
