<template>
  <div class="auth-container">


    <!-- Main content -->
    <div class="relative z-10 min-h-screen flex items-center justify-center p-6">
      <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
          <h1 class="holographic-text text-5xl font-bold mb-4">ROOM CHAT</h1>
          <p class="text-white/80 text-lg font-medium">Chào mừng bạn quay trở lại</p>
          <div class="w-20 h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent mx-auto mt-4"></div>
        </div>

        <!-- Login Card -->
        <div class="liquid-glass-card neon-glow p-8">
          <div class="space-y-6">
            <!-- Username Field -->
            <div class="floating-label">
              <input
                v-model="username"
                type="text"
                class="liquid-input w-full"
                placeholder=" "
                autocomplete="username"
              />
              <label>Tên đăng nhập</label>
            </div>

            <!-- Password Field -->
            <div class="floating-label">
              <input
                v-model="password"
                :type="showPassword ? 'text' : 'password'"
                class="liquid-input w-full pr-12"
                placeholder=" "
                autocomplete="current-password"
              />
              <label>Mật khẩu</label>

              <!-- Password toggle -->
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white/80 transition-colors z-10"
              >
                <svg v-if="!showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
                <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                </svg>
              </button>
            </div>

            <!-- Message Display -->
            <div v-if="message"
                 class="text-center p-4 rounded-xl"
                 :class="message.includes('thành công') ? 'message-success' : 'message-error'">
              {{ message }}
            </div>

            <!-- Login Button -->
            <button
              @click="testLogin"
              :disabled="loading"
              class="liquid-button w-full relative z-10"
            >
              <div v-if="loading" class="liquid-spinner mr-3"></div>
              {{ loading ? 'Đang xử lý...' : 'ĐĂNG NHẬP' }}
            </button>

            <!-- Divider -->
            <div class="relative">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-white/20"></div>
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-4 bg-black/20 text-white/60 rounded-full backdrop-blur-sm">hoặc</span>
              </div>
            </div>

            <!-- Register Link -->
            <div class="text-center">
              <p class="text-white/70 mb-3">Chưa có tài khoản?</p>
              <button
                @click="$router.push('/register')"
                class="liquid-button px-8 py-3 text-sm"
              >
                Đăng ký ngay
              </button>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8">
          <p class="text-white/50 text-sm">
            © {{ new Date().getFullYear() }} tungduong88. Powered by Tung Duong Technology.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth.js'

const router = useRouter()
const authStore = useAuthStore()

const username = ref('')
const password = ref('')
const loading = ref(false)
const message = ref('')
const showPassword = ref(false)

const testLogin = async () => {
  if (!username.value || !password.value) {
    message.value = 'Vui lòng nhập đầy đủ thông tin'
    return
  }

  try {
    loading.value = true
    message.value = 'Đang đăng nhập...'

    const credentials = {
      username: username.value,
      password: password.value
    }

    const result = await authStore.login(credentials)

    if (result.success) {
      message.value = 'Đăng nhập thành công!'
      router.push('/chat')
    } else {
      message.value = result.message
    }
  } catch (error) {
    console.error('Login error:', error)
    message.value = 'Có lỗi xảy ra, vui lòng thử lại'
  } finally {
    loading.value = false
  }
}
</script>