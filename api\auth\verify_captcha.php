<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['captcha']) || !isset($_SESSION['captcha'])) {
    jsonResponse(['success' => false, 'message' => 'Captcha không hợp lệ'], 400);
}

$user_captcha = strtoupper(trim($input['captcha']));
$session_captcha = strtoupper($_SESSION['captcha']);

if ($user_captcha === $session_captcha) {
    jsonResponse(['success' => true, 'message' => 'Captcha đúng']);
} else {
    jsonResponse(['success' => false, 'message' => 'Captcha không đúng'], 400);
}

unset($_SESSION['captcha']);
?>
