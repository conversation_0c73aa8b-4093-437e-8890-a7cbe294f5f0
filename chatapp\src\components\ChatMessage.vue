<template>
  <div :class="['message', { 'message-own': isOwn, 'message-other': !isOwn }]">
    <div class="message-avatar">
      <n-avatar
        round
        size="medium"
        :src="message.avatar || '/default-avatar.png'"
        @click="showUserInfo"
      />
    </div>
    
    <div class="message-content">
      <div class="message-header">
        <span class="username" @click="showUserInfo">
          {{ message.nickname || message.username }}
        </span>
        <n-tag v-if="message.is_admin === '1'" type="error" size="tiny">
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C14.8,12.4 14.4,13.2 13.7,13.7V16.5C13.7,17.3 13.1,18 12.3,18H11.7C10.9,18 10.3,17.3 10.3,16.5V13.8C9.6,13.3 9.2,12.5 9.2,11.6V10C9.2,8.6 10.6,7 12,7Z"/>
              </svg>
            </n-icon>
          </template>
          Admin
        </n-tag>
        <span v-if="message.location" class="location">
          ({{ message.location }})
        </span>
        <span class="timestamp">
          {{ formatTime(message.created_at) }}
        </span>
      </div>
      
      <div :class="['message-bubble', { 'bubble-own': isOwn, 'bubble-other': !isOwn }]">
        <div v-html="formatMessage(message.content)"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  isOwn: {
    type: Boolean,
    default: false
  }
})

const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) {
    return 'Vừa xong'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)} phút trước`
  } else if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('vi-VN', { 
      day: '2-digit', 
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

const formatMessage = (content) => {
  return content
    .replace(/\n/g, '<br>')
    .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" rel="noopener">$1</a>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
}

const showUserInfo = () => {
  console.log('Show user info for:', props.message.username)
}
</script>

<style scoped>
.message {
  display: flex;
  margin-bottom: 16px;
  max-width: 80%;
}

.message-own {
  flex-direction: row-reverse;
  margin-left: auto;
}

.message-other {
  flex-direction: row;
  margin-right: auto;
}

.message-avatar {
  margin: 0 12px;
  cursor: pointer;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
}

.message-own .message-header {
  justify-content: flex-end;
}

.username {
  font-weight: 600;
  cursor: pointer;
  color: #18a058;
}

.message-own .username {
  color: #2080f0;
}

.location {
  color: #999;
  font-size: 11px;
}

.timestamp {
  color: #999;
  font-size: 11px;
  margin-left: auto;
}

.message-own .timestamp {
  margin-left: 0;
  margin-right: auto;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.bubble-other {
  background: #f0f0f0;
  color: #333;
  border-bottom-left-radius: 4px;
}

.bubble-own {
  background: #2080f0;
  color: white;
  border-bottom-right-radius: 4px;
}

.message-bubble :deep(a) {
  color: inherit;
  text-decoration: underline;
}

.message-bubble :deep(strong) {
  font-weight: 600;
}

.message-bubble :deep(em) {
  font-style: italic;
}
</style>
