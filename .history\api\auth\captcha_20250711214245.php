<?php
require_once '../config.php';
header('Content-Type: image/png');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

function generateMathCaptcha() {
    $num1 = rand(1, 10);
    $num2 = rand(1, 10);
    $operators = ['+', '-'];
    $operator = $operators[array_rand($operators)];

    if ($operator === '+') {
        $result = $num1 + $num2;
        $question = "$num1 + $num2 = ?";
    } else {
        // Đảm bảo kết quả không âm
        if ($num1 < $num2) {
            $temp = $num1;
            $num1 = $num2;
            $num2 = $temp;
        }
        $result = $num1 - $num2;
        $question = "$num1 - $num2 = ?";
    }

    return ['question' => $question, 'answer' => $result];
}

$captcha_data = generateMathCaptcha();
$_SESSION['captcha'] = $captcha_data['answer'];
$captcha_text = $captcha_data['question'];

$width = 192;
$height = 64;
$image = imagecreatetruecolor($width, $height);

// Tạo màu với alpha channel
$bg_color = imagecolorallocate($image, 45, 45, 55);
$text_colors = [
    imagecolorallocate($image, 255, 255, 255),
    imagecolorallocate($image, 200, 200, 255),
    imagecolorallocate($image, 255, 200, 200),
    imagecolorallocate($image, 200, 255, 200),
    imagecolorallocate($image, 255, 255, 200)
];
$line_color = imagecolorallocate($image, 120, 120, 140);
$noise_color = imagecolorallocate($image, 80, 80, 100);

// Fill background
imagefill($image, 0, 0, $bg_color);

// Add some gradient effect
for ($i = 0; $i < $height; $i++) {
    $alpha = (int)(255 * ($i / $height) * 0.1);
    $gradient_color = imagecolorallocate($image, 45 + $alpha, 45 + $alpha, 55 + $alpha);
    imageline($image, 0, $i, $width, $i, $gradient_color);
}

// Add noise
for ($i = 0; $i < 100; $i++) {
    imagesetpixel($image, rand(0, $width), rand(0, $height), $noise_color);
}

// Add lines
for ($i = 0; $i < 4; $i++) {
    imageline($image, rand(0, $width), rand(0, $height), rand(0, $width), rand(0, $height), $line_color);
}

// Draw text
$font_size = 5; // Built-in font size
$char_width = 28;
$start_x = ($width - (strlen($captcha_text) * $char_width)) / 2;

for ($i = 0; $i < strlen($captcha_text); $i++) {
    $char = $captcha_text[$i];
    $color = $text_colors[array_rand($text_colors)];

    $x = $start_x + ($i * $char_width) + rand(-3, 3);
    $y = ($height / 2) - 10 + rand(-5, 5);

    // Draw character with slight shadow effect
    imagestring($image, $font_size, $x + 1, $y + 1, $char, imagecolorallocate($image, 0, 0, 0));
    imagestring($image, $font_size, $x, $y, $char, $color);
}

imagepng($image);
imagedestroy($image);
?>
