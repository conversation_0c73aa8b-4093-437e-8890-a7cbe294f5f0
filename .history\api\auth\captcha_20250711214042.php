<?php
require_once '../config.php';
header('Content-Type: image/png');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

function generateCaptchaText($length = 5) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $captcha = '';
    for ($i = 0; $i < $length; $i++) {
        $captcha .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $captcha;
}

$captcha_text = generateCaptchaText();
$_SESSION['captcha'] = $captcha_text;

$width = 192;
$height = 64;
$image = imagecreatetruecolor($width, $height);

// Tạo màu với alpha channel
$bg_color = imagecolorallocate($image, 45, 45, 55);
$text_colors = [
    imagecolorallocate($image, 255, 255, 255),
    imagecolorallocate($image, 200, 200, 255),
    imagecolorallocate($image, 255, 200, 200),
    imagecolorallocate($image, 200, 255, 200),
    imagecolorallocate($image, 255, 255, 200)
];
$line_color = imagecolorallocate($image, 120, 120, 140);
$noise_color = imagecolorallocate($image, 80, 80, 100);

// Fill background
imagefill($image, 0, 0, $bg_color);

// Add some gradient effect
for ($i = 0; $i < $height; $i++) {
    $alpha = (int)(255 * ($i / $height) * 0.1);
    $gradient_color = imagecolorallocate($image, 45 + $alpha, 45 + $alpha, 55 + $alpha);
    imageline($image, 0, $i, $width, $i, $gradient_color);
}

// Add noise
for ($i = 0; $i < 100; $i++) {
    imagesetpixel($image, rand(0, $width), rand(0, $height), $noise_color);
}

// Add lines
for ($i = 0; $i < 4; $i++) {
    imageline($image, rand(0, $width), rand(0, $height), rand(0, $width), rand(0, $height), $line_color);
}

// Draw text
$font_size = 5; // Built-in font size
$char_width = 28;
$start_x = ($width - (strlen($captcha_text) * $char_width)) / 2;

for ($i = 0; $i < strlen($captcha_text); $i++) {
    $char = $captcha_text[$i];
    $color = $text_colors[array_rand($text_colors)];

    $x = $start_x + ($i * $char_width) + rand(-3, 3);
    $y = ($height / 2) - 10 + rand(-5, 5);

    // Draw character with slight shadow effect
    imagestring($image, $font_size, $x + 1, $y + 1, $char, imagecolorallocate($image, 0, 0, 0));
    imagestring($image, $font_size, $x, $y, $char, $color);
}

imagepng($image);
imagedestroy($image);
?>
