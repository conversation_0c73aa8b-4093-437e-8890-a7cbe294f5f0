<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

$user = requireAuth();

// Update last seen
$stmt = $pdo->prepare("UPDATE users SET last_seen = NOW() WHERE id = ?");
$stmt->execute([$user['id']]);

// Remove sensitive data
unset($user['password']);
unset($user['expires_at']);

jsonResponse([
    'success' => true,
    'user' => $user
]);
?>
