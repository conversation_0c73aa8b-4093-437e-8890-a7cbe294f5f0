<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white flex items-center justify-center p-4">
    <div class="w-full max-w-4xl">
      <!-- Header -->
      <div class="text-center mb-12">
        <div class="mb-6">
          <h1 class="holographic-text text-5xl font-bold mb-4">CHOOSE AVATAR</h1>
          <p class="text-white/80 text-lg font-medium">Hãy chọn một nhân vật bạn yêu thích để làm avatar!</p>
          <div class="w-20 h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent mx-auto mt-4"></div>
        </div>
      </div>

      <!-- Avatar Grid -->
      <div class="liquid-glass-card neon-glow p-8 mb-8">
        <div class="grid grid-cols-3 md:grid-cols-5 gap-6">
          <div v-for="(avatar, idx) in avatars" :key="avatar.stt" class="flex flex-col items-center group">
            <div class="relative">
              <img
                :src="`/${avatar.stt}.png`"
                :alt="avatar.name"
                class="w-20 h-20 md:w-24 md:h-24 rounded-full border-3 border-white/20 hover:border-purple-400 cursor-pointer transition-all duration-300 group-hover:scale-110 group-hover:shadow-lg group-hover:shadow-purple-500/30"
                :class="{
                  'border-purple-500 shadow-lg shadow-purple-500/50 scale-110': selected === avatar.stt
                }"
                @click="selectAvatar(avatar.stt)"
              />
              <div v-if="selected === avatar.stt" class="absolute -top-1 -right-1 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
            </div>
            <span class="mt-3 text-sm font-medium text-white/80 group-hover:text-white transition-colors text-center">{{ avatar.name }}</span>
          </div>
        </div>
      </div>

      <!-- Action Button -->
      <div class="text-center">
        <button
          class="liquid-button relative z-10 disabled:opacity-50 disabled:cursor-not-allowed"
          :disabled="!selected || loading"
          @click="submitAvatar"
        >
          {{ loading ? 'Đang cập nhật...' : 'XÁC NHẬN' }}
        </button>

        <!-- Messages -->
        <div v-if="message" class="mt-6 p-4 rounded-xl message-success">
          {{ message }}
        </div>
        <div v-if="errorMessage" class="mt-6 p-4 rounded-xl message-error">
          {{ errorMessage }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { authAPI } from '../../utils/api'

const router = useRouter()
const authStore = useAuthStore()
const selected = ref(null)
const loading = ref(false)
const message = ref('')
const errorMessage = ref('')

const avatars = [
  { stt: 1, name: 'Tiểu Bảo' },
  { stt: 2, name: 'Hắc Phong' },
  { stt: 3, name: 'Lãnh Nguyệt' },
  { stt: 4, name: 'Bạch Hổ' },
  { stt: 5, name: 'Tuyết Nhi' },
  { stt: 6, name: 'Hỏa Vũ' },
  { stt: 7, name: 'Tiêu Dao' },
  { stt: 8, name: 'Vô Danh' },
  { stt: 9, name: 'Thanh Vân' },
  { stt: 10, name: 'Huyết Ảnh' },
  { stt: 11, name: 'Mộng Tuyết' },
  { stt: 12, name: 'Hàn Phong' },
  { stt: 13, name: 'Vô Tâm' },
  { stt: 14, name: 'Tiểu Hồ' },
  { stt: 15, name: 'Diệp Vũ' },
]

onMounted(() => {
  if (localStorage.getItem('canSelectAvatar') !== '1') {
    router.replace('/register')
  }
})

function selectAvatar(stt) {
  selected.value = stt
}

async function submitAvatar() {
  if (!selected.value) return
  loading.value = true
  errorMessage.value = ''
  message.value = ''
  try {
    const res = await authAPI.updateAvatar({ avatar: `${selected.value}.png` })
    if (res.data.success) {
      message.value = 'Cập nhật avatar thành công!'
      await authStore.fetchUser()
      localStorage.removeItem('canSelectAvatar')
      setTimeout(() => {
        router.push('/login')
      }, 1000)
    } else {
      errorMessage.value = res.data.message || 'Cập nhật avatar thất bại!'
    }
  } catch (e) {
    errorMessage.value = 'Có lỗi xảy ra, vui lòng thử lại.'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.holographic-text {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: holographic 3s ease-in-out infinite;
}

@keyframes holographic {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.liquid-glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.neon-glow {
  box-shadow:
    0 0 20px rgba(139, 92, 246, 0.3),
    0 0 40px rgba(139, 92, 246, 0.2),
    0 0 80px rgba(139, 92, 246, 0.1);
}

.liquid-button {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8), rgba(59, 130, 246, 0.8));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 16px 32px;
  font-weight: 600;
  font-size: 16px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.liquid-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.4);
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.9), rgba(59, 130, 246, 0.9));
}

.liquid-button:active:not(:disabled) {
  transform: translateY(0);
}

.message-success {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: rgb(34, 197, 94);
  backdrop-filter: blur(10px);
}

.message-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: rgb(239, 68, 68);
  backdrop-filter: blur(10px);
}

img {
  object-fit: cover;
}
</style>