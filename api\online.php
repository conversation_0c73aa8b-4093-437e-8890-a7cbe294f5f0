<?php
require_once 'config.php';

$user = requireAuth();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

try {
    // Update current user's last seen
    $stmt = $pdo->prepare("UPDATE users SET last_seen = NOW() WHERE id = ?");
    $stmt->execute([$user['id']]);
    
    // Get online users (active in last 5 minutes)
    $stmt = $pdo->prepare("
        SELECT id, username, nickname, avatar, is_admin, location, ip, last_seen
        FROM users 
        WHERE status = 1 
        AND last_seen > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ORDER BY last_seen DESC
    ");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    jsonResponse([
        'success' => true,
        'users' => $users
    ]);
    
} catch (PDOException $e) {
    jsonResponse(['success' => false, 'message' => 'Lỗi khi tải danh sách online'], 500);
}
?>
