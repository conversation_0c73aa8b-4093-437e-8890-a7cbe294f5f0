<template>
  <div class="min-h-screen bg-gray-900 text-white p-4">
    <div class="max-w-md mx-auto mt-20">
      <h1 class="text-3xl font-bold text-center mb-8">ĐĂNG NHẬP</h1>

      <div class="bg-gray-800 p-6 rounded-lg">
        <div class="mb-4">
          <label class="block text-sm font-medium mb-2">Username:</label>
          <input
            v-model="username"
            type="text"
            class="w-full p-3 bg-gray-700 border border-gray-600 rounded text-white"
            placeholder="Nhập username"
          />
        </div>

        <div class="mb-4">
          <label class="block text-sm font-medium mb-2">Password:</label>
          <input
            v-model="password"
            type="password"
            class="w-full p-3 bg-gray-700 border border-gray-600 rounded text-white"
            placeholder="Nhập password"
          />
        </div>

        <div v-if="message" class="mb-4 p-3 bg-blue-600 rounded">
          {{ message }}
        </div>

        <button
          @click="testLogin"
          :disabled="loading"
          class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50"
        >
          {{ loading ? 'Đang xử lý...' : 'ĐĂNG NHẬP' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const username = ref('')
const password = ref('')
const loading = ref(false)
const message = ref('')

const testLogin = () => {
  console.log('Button clicked!')

  if (!username.value || !password.value) {
    message.value = 'Vui lòng nhập đầy đủ thông tin'
    return
  }

  loading.value = true
  message.value = 'Đang xử lý...'

  setTimeout(() => {
    message.value = `Đăng nhập với: ${username.value} / ${password.value}`
    loading.value = false
  }, 1000)
}
</script>