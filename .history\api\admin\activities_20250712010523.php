<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(false, 'Method not allowed', null, 405);
}

$user = verifyToken();
if (!$user) {
    jsonResponse(false, 'Token không hợp lệ', null, 401);
}

if (!checkAdminRole($user['id'])) {
    jsonResponse(false, 'Không có quyền truy cập', null, 403);
}

try {
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $limit = min($limit, 100); // Giới hạn tối đa 100 records

    // Lấy danh sách hoạt động gần đây
    $stmt = $pdo->prepare("
        SELECT 
            aa.*,
            u.username as admin_username,
            u.nickname as admin_nickname,
            u.avatar as admin_avatar
        FROM admin_activities aa
        LEFT JOIN users u ON aa.admin_id = u.id
        ORDER BY aa.created_at DESC
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    $activities = $stmt->fetchAll();

    // Format dữ liệu
    $formattedActivities = [];
    foreach ($activities as $activity) {
        $formattedActivities[] = [
            'id' => (int)$activity['id'],
            'admin_id' => (int)$activity['admin_id'],
            'admin_username' => $activity['admin_username'],
            'admin_nickname' => $activity['admin_nickname'] ?: $activity['admin_username'],
            'admin_avatar' => $activity['admin_avatar'],
            'action' => $activity['action'],
            'target_type' => $activity['target_type'],
            'target_id' => $activity['target_id'] ? (int)$activity['target_id'] : null,
            'description' => $activity['description'],
            'ip' => $activity['ip'],
            'created_at' => $activity['created_at']
        ];
    }

    // Ghi log hoạt động admin
    $stmt = $pdo->prepare("INSERT INTO admin_activities (admin_id, action, description, ip) VALUES (?, ?, ?, ?)");
    $stmt->execute([
        $user['id'],
        'view_activities',
        'Xem danh sách hoạt động',
        $_SERVER['REMOTE_ADDR']
    ]);

    jsonResponse(true, 'Lấy danh sách hoạt động thành công', ['activities' => $formattedActivities]);

} catch (Exception $e) {
    error_log("Activities API Error: " . $e->getMessage());
    jsonResponse(false, 'Lỗi server', null, 500);
}
