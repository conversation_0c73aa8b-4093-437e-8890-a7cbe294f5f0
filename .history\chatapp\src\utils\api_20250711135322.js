import axios from 'axios'
import { useAuthStore } from '../stores/auth'

const API_BASE_URL = ''

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export default api

export const authAPI = {
  login: (credentials) => api.post('/api/auth/login.php', credentials),
  register: (userData) => api.post('/api/auth/register.php', userData),
  getUser: () => api.get('/api/auth/user.php'),
  logout: () => api.post('/api/auth/logout.php')
}

export const chatAPI = {
  getMessages: (lastId = 0) => api.get(`/api/messages.php?last_id=${lastId}`),
  sendMessage: (content) => api.post('/api/messages.php', { content }),
  getOnlineUsers: () => api.get('/api/online.php'),
  updateLocation: (location) => api.post('/api/update_location.php', { location })
}

export const adminAPI = {
  getStats: () => api.get('/api/admin/stats.php'),
  getActivities: () => api.get('/api/admin/activities.php'),
  
  getUsers: () => api.get('/api/admin/users.php'),
  createUser: (userData) => api.post('/api/admin/users.php', userData),
  updateUser: (userId, userData) => api.put(`/api/admin/users.php?id=${userId}`, userData),
  deleteUser: (userId) => api.delete(`/api/admin/users.php?id=${userId}`),
  
  getMessages: () => api.get('/api/admin/messages.php'),
  deleteMessage: (messageId) => api.delete(`/api/admin/messages.php?id=${messageId}`),
  
  getBlacklist: () => api.get('/api/admin/blacklist.php'),
  addToBlacklist: (ipData) => api.post('/api/admin/blacklist.php', ipData),
  updateBlacklist: (ipId, ipData) => api.put(`/api/admin/blacklist.php?id=${ipId}`, ipData),
  removeFromBlacklist: (ipId) => api.delete(`/api/admin/blacklist.php?id=${ipId}`)
}
