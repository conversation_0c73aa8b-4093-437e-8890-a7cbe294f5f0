<?php
$cloud_name = 'dv6hnveua';
$api_key = '216189142954892';
$api_secret = 'QumSlDqAY7-4FDWF5s9Cp8yu9P4';

$timestamp = time();
$signature = sha1('timestamp=' . $timestamp . $api_secret);

echo "Testing Cloudinary upload...\n";
echo "Cloud name: $cloud_name\n";
echo "API key: $api_key\n";
echo "Timestamp: $timestamp\n";
echo "Signature: $signature\n";

$test_image_url = 'https://via.placeholder.com/300x200.jpg';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://api.cloudinary.com/v1_1/$cloud_name/image/upload");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_POSTFIELDS, [
    'file' => $test_image_url,
    'api_key' => $api_key,
    'timestamp' => $timestamp,
    'signature' => $signature
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "\nHTTP Code: $http_code\n";
echo "CURL Error: $curl_error\n";
echo "Response: $response\n";

if ($http_code === 200) {
    $data = json_decode($response, true);
    if (isset($data['secure_url'])) {
        echo "\nSuccess! Image URL: " . $data['secure_url'] . "\n";
    }
} else {
    echo "\nFailed to upload\n";
}
?>
