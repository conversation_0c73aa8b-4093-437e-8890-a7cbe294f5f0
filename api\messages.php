<?php
require_once 'config.php';

$user = requireAuth();

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Get messages
    $lastId = isset($_GET['last_id']) ? (int)$_GET['last_id'] : 0;
    
    try {
        $stmt = $pdo->prepare("
            SELECT m.*, u.username, u.nickname, u.avatar, u.is_admin, u.location
            FROM messages m
            JOIN users u ON m.user_id = u.id
            WHERE m.id > ?
            ORDER BY m.created_at ASC
            LIMIT 50
        ");
        $stmt->execute([$lastId]);
        $messages = $stmt->fetchAll();
        
        jsonResponse([
            'success' => true,
            'messages' => $messages
        ]);
        
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Lỗi khi tải tin nhắn'], 500);
    }
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Send message
    $input = json_decode(file_get_contents('php://input'), true);
    
    $content = trim($input['content'] ?? '');
    $img = trim($input['img'] ?? ''); // link ảnh Cloudinary, có thể rỗng
    $ip = getUserIP();
    
    if ($content === '' && $img === '') {
        jsonResponse(['success' => false, 'message' => 'Nội dung hoặc ảnh không được để trống'], 400);
    }
    // Check message length
    if (strlen($content) > 1000) {
        jsonResponse(['success' => false, 'message' => 'Tin nhắn quá dài (tối đa 1000 ký tự)'], 400);
    }
    try {
        $stmt = $pdo->prepare("
            INSERT INTO messages (user_id, content, img, ip, location) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $user['id'],
            $content,
            $img,
            $ip,
            $user['location']
        ]);
        
        jsonResponse([
            'success' => true,
            'message' => 'Gửi tin nhắn thành công'
        ]);
        
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Lỗi khi gửi tin nhắn'], 500);
    }
    
} else {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}
?>
