<template>
  <div class="min-h-screen bg-slate-900 text-white flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <div class="text-center mb-8">
        <h1 class="text-5xl font-bold mb-2 text-purple-400">TUNGDUONGCMS</h1>
        <p class="text-slate-400">Chào mừng bạn quay trở lại</p>
      </div>

      <div class="bg-slate-800/60 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-8 shadow-2xl">
        <div class="space-y-6">
          <div>
            <label for="username" class="block text-sm font-medium text-slate-300 mb-2">Tên đăng nhập</label>
            <div class="relative">
              <input
                v-model="username"
                name="username"
                id="username"
                type="text"
                class="block w-full bg-slate-700/50 border border-slate-600 rounded-lg py-3 px-4 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition"
                placeholder="Nhập tên đăng nhập"
              />
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                 <svg class="h-5 w-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>
              </div>
            </div>
            <p v-if="errors.username" class="text-red-400 text-sm mt-1">{{ errors.username }}</p>
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-slate-300 mb-2">Mật khẩu</label>
            <div class="relative">
              <input
                v-model="password"
                name="password"
                id="password"
                :type="showPassword ? 'text' : 'password'"
                class="block w-full bg-slate-700/50 border border-slate-600 rounded-lg py-3 px-4 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition"
                placeholder="Nhập mật khẩu"
              />
              <button type="button" @click="showPassword = !showPassword" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg v-if="!showPassword" class="h-5 w-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>
                <svg v-else class="h-5 w-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path></svg>
              </button>
            </div>
            <p v-if="errors.password" class="text-red-400 text-sm mt-1">{{ errors.password }}</p>
          </div>

          <div v-if="errorMessage" class="p-3 rounded-lg bg-red-500/20 border border-red-500/30">
            <p class="text-red-200 text-sm">{{ errorMessage }}</p>
          </div>

          <div>
            <button
              type="button"
              :disabled="loading"
              @click="handleLogin"
              class="w-full flex justify-center bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-transform transform hover:scale-105 disabled:opacity-50"
            >
              <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ loading ? 'Đang xử lý...' : 'Đăng nhập' }}
            </button>
          </div>
        </div>

        <div class="mt-6 text-center">
          <p class="text-slate-400">
            Chưa có tài khoản?
            <router-link to="/register" class="font-medium text-purple-400 hover:text-purple-300">
              Đăng ký ngay
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const showPassword = ref(false)
const errorMessage = ref('')

const formData = reactive({
  username: '',
  password: ''
})

const errors = ref({})

const handleLogin = async () => {
  console.log('Login button clicked!')

  // Reset errors
  errors.value = {}

  // Simple validation
  if (!formData.username.trim()) {
    errors.value.username = 'Vui lòng nhập tên đăng nhập'
    return
  }

  if (!formData.password.trim()) {
    errors.value.password = 'Vui lòng nhập mật khẩu'
    return
  }

  try {
    loading.value = true
    errorMessage.value = ''

    console.log('Attempting login with:', formData)

    const result = await authStore.login(formData)

    console.log('Login result:', result)

    if (result.success) {
      console.log('Login successful, NOT redirecting yet...')
      errorMessage.value = 'Đăng nhập thành công!'
      // Không redirect ngay, để test
      // await router.push('/chat')
    } else {
      errorMessage.value = result.message || 'Đăng nhập thất bại'
    }
  } catch (error) {
    console.error('Login error:', error)
    errorMessage.value = 'Có lỗi xảy ra, vui lòng thử lại'
  } finally {
    loading.value = false
  }
}

// Expose for template
const username = ref('')
const password = ref('')

// Watch for changes and update formData
import { watch } from 'vue'
watch(username, (val) => formData.username = val)
watch(password, (val) => formData.password = val)
</script>