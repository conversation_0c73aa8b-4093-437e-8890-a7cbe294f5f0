<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

echo "=== DEBUG API ===\n";

// Test database connection
$db_host = 'localhost';
$db_name = 'group_chat_app';
$db_user = 'root';
$db_pass = '';

try {
    echo "1. Testing database connection...\n";
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected successfully!\n";
    
    // Test if tables exist
    echo "\n2. Checking tables...\n";
    $tables = ['users', 'messages', 'ip_blacklist', 'user_sessions'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' exists\n";
        } else {
            echo "❌ Table '$table' NOT found\n";
        }
    }
    
    // Test users table
    echo "\n3. Checking users...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "Users count: " . $result['count'] . "\n";
    
    if ($result['count'] > 0) {
        $stmt = $pdo->query("SELECT username, email, is_admin FROM users LIMIT 3");
        $users = $stmt->fetchAll();
        echo "Sample users:\n";
        foreach ($users as $user) {
            echo "- {$user['username']} ({$user['email']}) - Admin: {$user['is_admin']}\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

echo "\n=== END DEBUG ===";
?>
