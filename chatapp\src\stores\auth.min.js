// @ts-nocheck
import{defineStore}from"pinia";import{ref,computed}from"vue";import{authAPI}from"../utils/api";export const useAuthStore=defineStore("auth",(()=>{const e=ref(JSON.parse(localStorage.getItem("user")||"null")),s=ref(localStorage.getItem("token")),t=computed((()=>!!s.value)),a=computed((()=>1===e.value?.is_admin||"1"===e.value?.is_admin)),r=()=>{e.value=null,s.value=null,localStorage.removeItem("token"),localStorage.removeItem("user")},u=async()=>{try{const s=await authAPI.getUser();return s.data.success?(e.value=s.data.user,localStorage.setItem("user",JSON.stringify(s.data.user)),{success:!0}):{success:!1}}catch(e){return r(),{success:!1}}};return{user:e,token:s,isAuthenticated:t,isAdmin:a,login:async t=>{try{const a=await authAPI.login(t);return a.data.success?(s.value=a.data.token,e.value=a.data.user,localStorage.setItem("token",s.value),localStorage.setItem("user",JSON.stringify(a.data.user)),{success:!0,user:a.data.user}):{success:!1,message:a.data.message}}catch(e){return e.response&&401===e.response.status?{success:!1,message:"Tên đăng nhập hoặc mật khẩu không đúng."}:{success:!1,message:"Lỗi không xác định, vui lòng thử lại."}}},register:async e=>{try{return(await authAPI.register(e)).data}catch(e){return e.response&&e.response.data&&e.response.data.message?{success:!1,message:e.response.data.message}:{success:!1,message:"Đăng ký thất bại"}}},logout:r,fetchUser:u,initAuth:async()=>{s.value&&!e.value&&await u()}}}));