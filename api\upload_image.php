<?php
require_once 'config.php';
$user = requireAuth();

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    jsonResponse(['success' => false, 'message' => 'Không nhận được file ảnh'], 400);
}

$filePath = $_FILES['image']['tmp_name'];
$fileName = $_FILES['image']['name'];

$allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
$fileType = mime_content_type($filePath);

if (!in_array($fileType, $allowedTypes)) {
    jsonResponse(['success' => false, 'message' => 'Định dạng file không được hỗ trợ'], 400);
}

if ($_FILES['image']['size'] > 5 * 1024 * 1024) {
    jsonResponse(['success' => false, 'message' => 'File quá lớn (tối đa 5MB)'], 400);
}

$cloud_name = 'dv6hnveua';

try {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://api.cloudinary.com/v1_1/$cloud_name/image/upload");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_POSTFIELDS, [
        'file' => new CURLFile($filePath, $fileType, $fileName),
        'upload_preset' => 'chat_images'
    ]);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    if ($curl_error) {
        jsonResponse(['success' => false, 'message' => 'Lỗi kết nối: ' . $curl_error], 500);
    }

    if ($http_code !== 200) {
        $error_data = json_decode($response, true);
        $error_msg = isset($error_data['error']['message']) ? $error_data['error']['message'] : 'Lỗi không xác định';
        jsonResponse(['success' => false, 'message' => 'Lỗi upload: ' . $error_msg, 'http_code' => $http_code, 'response' => $response], 500);
    }

    $data = json_decode($response, true);
    if (!$data || !isset($data['secure_url'])) {
        jsonResponse(['success' => false, 'message' => 'Không nhận được link ảnh từ Cloudinary', 'response' => $response], 500);
    }

    jsonResponse([
        'success' => true,
        'url' => $data['secure_url']
    ]);

} catch (Exception $e) {
    jsonResponse(['success' => false, 'message' => 'Lỗi server: ' . $e->getMessage()], 500);
}
