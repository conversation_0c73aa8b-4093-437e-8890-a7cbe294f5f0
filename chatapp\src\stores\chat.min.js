// @ts-nocheck
import{defineStore}from"pinia";import{ref,computed}from"vue";import{chatAPI}from"../utils/api";export const useChatStore=defineStore("chat",(()=>{const e=ref([]),s=ref([]),a=ref(0),t=ref(!0),r=computed((()=>e.value.sort(((e,s)=>new Date(e.created_at)-new Date(s.created_at))))),c=async()=>{try{const s=await chatAPI.getMessages(a.value);s.data.messages&&s.data.messages.length>0&&s.data.messages.forEach((s=>{e.value.find((e=>e.id===s.id))||(e.value.push(s),a.value=Math.max(a.value,s.id))}))}catch(e){console.error("Lỗi khi tải tin nhắn:",e)}},n=async()=>{try{const e=await chatAPI.getOnlineUsers();e.data.users&&(s.value=e.data.users)}catch(e){console.error("Lỗi khi tải danh sách online:",e)}};return{messages:e,onlineUsers:s,lastMessageId:a,isScrolledToBottom:t,sortedMessages:r,fetchMessages:c,sendMessage:async e=>{try{const s=await chatAPI.sendMessage(e);return s.data.success?(await c(),{success:!0}):{success:!1,message:s.data.message}}catch(e){return{success:!1,message:"Gửi tin nhắn thất bại"}}},fetchOnlineUsers:n,updateLocation:async e=>{try{await chatAPI.updateLocation(e)}catch(e){console.error("Lỗi khi cập nhật vị trí:",e)}},startPolling:()=>{setInterval((()=>{c(),n()}),2e3)}}}));