<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

$user = requireAuth();

try {
    // Lấy danh sách admin
    $stmt = $pdo->prepare("
        SELECT 
            id, username, nickname, avatar, last_seen,
            CASE 
                WHEN last_seen >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 1 
                ELSE 0 
            END as is_online
        FROM users 
        WHERE is_admin = 1 AND status = 1
        ORDER BY is_online DESC, last_seen DESC
    ");
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
    // Format dữ liệu
    $formattedAdmins = [];
    foreach ($admins as $admin) {
        $formattedAdmins[] = [
            'id' => (int)$admin['id'],
            'username' => $admin['username'],
            'nickname' => $admin['nickname'] ?: $admin['username'],
            'avatar' => $admin['avatar'],
            'last_seen' => $admin['last_seen'],
            'is_online' => (int)$admin['is_online']
        ];
    }
    
    // Nếu user là admin, lấy thêm thống kê tin nhắn chưa đọc
    if ($user['is_admin'] == 1) {
        foreach ($formattedAdmins as &$admin) {
            if ($admin['id'] == $user['id']) {
                // Đếm tin nhắn chưa đọc cho admin này
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as unread_count 
                    FROM admin_messages 
                    WHERE admin_id = ? AND is_read = 0 AND sender_id != ?
                ");
                $stmt->execute([$user['id'], $user['id']]);
                $admin['unread_count'] = (int)$stmt->fetch()['unread_count'];
            }
        }
    }
    
    jsonResponse([
        'success' => true,
        'message' => 'Lấy danh sách admin thành công',
        'admins' => $formattedAdmins
    ]);

} catch (Exception $e) {
    error_log("List Admins API Error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Lỗi server'], 500);
}
