# Gemini Configuration for admin

This file provides context to the Gemini AI assistant for the `admin` directory.

## Admin Panel (PHP)

This directory contains the PHP-based admin panel.

-   **Functionality:** The admin panel is used for managing users, messages, and other aspects of the application.
-   **Authentication:** The admin panel has its own authentication system.
-   **Styling:** The admin panel uses custom CSS (`css/app.css`).

### Coding Conventions

-   Maintain the existing PHP coding style.
-   Use the `header.php` file for the common page header.
