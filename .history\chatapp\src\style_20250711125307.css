@import url('https://fonts.googleapis.com/css2?family=Play:wght@400;700&family=Roboto+Slab:wght@400;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-700;
  }

  body {
    @apply bg-slate-900 text-gray-100 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Roboto Slab', serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Play', sans-serif;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300;
  }

  .btn-ghost {
    @apply btn hover:bg-gray-100 hover:text-gray-900;
  }

  .btn-outline {
    @apply btn border border-gray-300 hover:bg-gray-100 hover:text-gray-900;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm;
  }

  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900;
  }
}
