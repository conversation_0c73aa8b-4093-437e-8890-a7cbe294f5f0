<template>
  <div class="min-h-screen bg-gray-900 text-white p-4">
    <div class="max-w-md mx-auto mt-20">
      <h1 class="text-3xl font-bold text-center mb-8">ĐĂNG NHẬP</h1>

      <div class="bg-gray-800 p-6 rounded-lg">
        <div class="mb-4">
          <label class="block text-sm font-medium mb-2">Username:</label>
          <input
            v-model="username"
            type="text"
            class="w-full p-3 bg-gray-700 border border-gray-600 rounded text-white"
            placeholder="Nhập username"
          />
        </div>

        <div class="mb-4">
          <label class="block text-sm font-medium mb-2">Password:</label>
          <input
            v-model="password"
            type="password"
            class="w-full p-3 bg-gray-700 border border-gray-600 rounded text-white"
            placeholder="Nhập password"
          />
        </div>

        <div v-if="message" class="mb-4 p-3 bg-blue-600 rounded">
          {{ message }}
        </div>

        <button
          @click="testLogin"
          :disabled="loading"
          class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50"
        >
          {{ loading ? 'Đang xử lý...' : 'ĐĂNG NHẬP' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const username = ref('')
const password = ref('')
const loading = ref(false)
const message = ref('')

const testLogin = async () => {
  console.log('Login button clicked!')

  if (!username.value || !password.value) {
    message.value = 'Vui lòng nhập đầy đủ thông tin'
    return
  }

  try {
    loading.value = true
    message.value = 'Đang đăng nhập...'

    const credentials = {
      username: username.value,
      password: password.value
    }

    console.log('Attempting login with:', credentials)

    const result = await authStore.login(credentials)

    console.log('Login result:', result)

    if (result.success) {
      message.value = 'Đăng nhập thành công!'
      console.log('Redirecting to chat...')

      setTimeout(() => {
        router.push('/chat')
      }, 1000)
    } else {
      message.value = result.message || 'Đăng nhập thất bại'
    }
  } catch (error) {
    console.error('Login error:', error)
    message.value = 'Có lỗi xảy ra, vui lòng thử lại'
  } finally {
    loading.value = false
  }
}
</script>