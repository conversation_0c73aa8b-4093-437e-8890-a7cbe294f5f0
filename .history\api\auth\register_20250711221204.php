<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['username']) || !isset($input['email']) || !isset($input['password'])) {
    jsonResponse(['success' => false, 'message' => 'Tất cả các trường là bắt buộc'], 400);
}

$username = trim($input['username']);
$email = trim($input['email']);
$password = $input['password'];
$ip = getUserIP();
$location = getLocation($ip);


// Check if IP is blacklisted
if (isBlacklisted($ip)) {
    jsonResponse(['success' => false, 'message' => 'IP của bạn đã bị chặn'], 403);
}

// Validation
if (strlen($username) < 3 || strlen($username) > 20) {
    jsonResponse(['success' => false, 'message' => 'Tên đăng nhập phải từ 3-20 ký tự'], 400);
}

if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    jsonResponse(['success' => false, 'message' => 'Email không hợp lệ'], 400);
}

if (strlen($password) < 6) {
    jsonResponse(['success' => false, 'message' => 'Mật khẩu phải có ít nhất 6 ký tự'], 400);
}

try {
    // Check if username exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
    $stmt->execute([$username]);

    if ($stmt->fetchColumn() > 0) {
        jsonResponse(['success' => false, 'message' => 'Tên đăng nhập đã tồn tại'], 409);
    }

    // Check if email exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
    $stmt->execute([$email]);

    if ($stmt->fetchColumn() > 0) {
        jsonResponse(['success' => false, 'message' => 'Email đã được sử dụng'], 409);
    }
    
    // Create user
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password, nickname, ip, location, last_seen) 
        VALUES (?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $username,
        $email,
        $hashedPassword,
        $username, // Default nickname is username
        $ip,
        json_encode($location)
    ]);
    
    unset($_SESSION['captcha']);

    jsonResponse([
        'success' => true,
        'message' => 'Đăng ký thành công! Vui lòng đăng nhập.'
    ]);

} catch (PDOException $e) {
    jsonResponse(['success' => false, 'message' => 'Lỗi hệ thống'], 500);
}
?>
