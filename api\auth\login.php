<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['username']) || !isset($input['password'])) {
    jsonResponse(['success' => false, 'message' => 'Username và password là bắt buộc'], 400);
}

$username = trim($input['username']);
$password = $input['password'];
$ip = getUserIP();

// Check if IP is blacklisted
if (isBlacklisted($ip)) {
    jsonResponse(['success' => false, 'message' => 'IP của bạn đã bị chặn'], 403);
}

try {
    // Find user
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? AND status = 1");
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    if (!$user || !password_verify($password, $user['password'])) {
        jsonResponse(['success' => false, 'message' => 'Tên đăng nhập hoặc mật khẩu không đúng'], 401);
    }

    // Set default room (không cần user_rooms table)
    $user['default_room_id'] = null;
    
    // Generate token
    $token = generateToken();
    $expires = date('Y-m-d H:i:s', strtotime('+7 days'));
    
    // Save session
    $stmt = $pdo->prepare("
        INSERT INTO user_sessions (user_id, token, ip, user_agent, expires_at) 
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $user['id'],
        $token,
        $ip,
        $_SERVER['HTTP_USER_AGENT'] ?? '',
        $expires
    ]);
    
    // Update user last login and token
    $stmt = $pdo->prepare("
        UPDATE users
        SET last_login = NOW(), ip = ?, last_seen = NOW(), token = ?
        WHERE id = ?
    ");
    $stmt->execute([$ip, $token, $user['id']]);
    
    // Remove password from response
    unset($user['password']);
    
    jsonResponse([
        'success' => true,
        'token' => $token,
        'user' => $user,
        'message' => 'Đăng nhập thành công'
    ]);
    
} catch (PDOException $e) {
    jsonResponse(['success' => false, 'message' => 'Lỗi hệ thống'], 500);
}
?>
