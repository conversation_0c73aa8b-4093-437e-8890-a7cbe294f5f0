<?php
require_once '../config.php';

$user = requireAdmin();

try {
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetBlacklist($user);
            break;
        case 'POST':
            handleAddToBlacklist($user);
            break;
        case 'PUT':
            handleUpdateBlacklist($user);
            break;
        case 'DELETE':
            handleRemoveFromBlacklist($user);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['success' => false, 'message' => 'Lỗi server'], 500);
}

function handleGetBlacklist($admin) {
    global $pdo;
    
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(1, (int)$_GET['limit'])) : 20;
    $offset = ($page - 1) * $limit;
    
    $whereClause = '';
    $params = [];
    
    if ($search) {
        $whereClause = "WHERE bl.ip LIKE ? OR bl.reason LIKE ?";
        $searchParam = "%$search%";
        $params = [$searchParam, $searchParam];
    }
    
    // Lấy tổng số IPs
    $countStmt = $pdo->prepare("SELECT COUNT(*) as total FROM ip_blacklist bl $whereClause");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    // Lấy danh sách blacklist
    $stmt = $pdo->prepare("
        SELECT
            bl.id, bl.ip, bl.reason, bl.created_at,
            u.username as admin_username, u.nickname as admin_nickname
        FROM ip_blacklist bl
        LEFT JOIN users u ON bl.admin_id = u.id
        $whereClause
        ORDER BY bl.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $executeParams = array_merge($params, [$limit, $offset]);
    $stmt->execute($executeParams);
    $blacklist = $stmt->fetchAll();
    
    // Format dữ liệu
    $formattedBlacklist = [];
    foreach ($blacklist as $item) {
        $formattedBlacklist[] = [
            'id' => (int)$item['id'],
            'ip' => $item['ip'],
            'reason' => $item['reason'],
            'admin_username' => $item['admin_username'],
            'admin_nickname' => $item['admin_nickname'] ?: $item['admin_username'],
            'created_at' => $item['created_at']
        ];
    }
    
    // Ghi log
    logAdminActivity($admin['id'], 'view_blacklist', 'Xem danh sách IP bị cấm');
    
    jsonResponse([
        'success' => true,
        'message' => 'Lấy danh sách IP bị cấm thành công',
        'blacklist' => $formattedBlacklist,
        'pagination' => [
            'total' => (int)$total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function handleAddToBlacklist($admin) {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $ip = trim($input['ip'] ?? '');
    $reason = trim($input['reason'] ?? '');
    
    // Validate
    if (empty($ip)) {
        jsonResponse(['success' => false, 'message' => 'IP không được để trống'], 400);
    }
    
    if (!filter_var($ip, FILTER_VALIDATE_IP)) {
        jsonResponse(['success' => false, 'message' => 'IP không hợp lệ'], 400);
    }
    
    // Kiểm tra IP đã tồn tại
    $stmt = $pdo->prepare("SELECT id FROM ip_blacklist WHERE ip = ?");
    $stmt->execute([$ip]);
    if ($stmt->fetch()) {
        jsonResponse(['success' => false, 'message' => 'IP đã có trong danh sách đen'], 400);
    }
    
    // Thêm vào blacklist
    $stmt = $pdo->prepare("INSERT INTO ip_blacklist (ip, reason, admin_id) VALUES (?, ?, ?)");
    $stmt->execute([$ip, $reason, $admin['id']]);
    
    $blacklistId = $pdo->lastInsertId();
    
    // Ghi log
    logAdminActivity($admin['id'], 'add_blacklist', "Thêm IP vào danh sách đen: $ip", 'blacklist', $blacklistId);
    
    jsonResponse([
        'success' => true,
        'message' => 'Thêm IP vào danh sách đen thành công',
        'blacklist_id' => (int)$blacklistId
    ]);
}

function handleUpdateBlacklist($admin) {
    global $pdo;
    
    $blacklistId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if (!$blacklistId) {
        jsonResponse(['success' => false, 'message' => 'ID không hợp lệ'], 400);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $ip = trim($input['ip'] ?? '');
    $reason = trim($input['reason'] ?? '');
    
    // Validate
    if (empty($ip)) {
        jsonResponse(['success' => false, 'message' => 'IP không được để trống'], 400);
    }
    
    if (!filter_var($ip, FILTER_VALIDATE_IP)) {
        jsonResponse(['success' => false, 'message' => 'IP không hợp lệ'], 400);
    }
    
    // Kiểm tra blacklist tồn tại
    $stmt = $pdo->prepare("SELECT ip FROM ip_blacklist WHERE id = ?");
    $stmt->execute([$blacklistId]);
    $existingItem = $stmt->fetch();
    if (!$existingItem) {
        jsonResponse(['success' => false, 'message' => 'Mục không tồn tại'], 404);
    }
    
    // Kiểm tra IP trùng lặp (trừ chính mục này)
    $stmt = $pdo->prepare("SELECT id FROM ip_blacklist WHERE ip = ? AND id != ?");
    $stmt->execute([$ip, $blacklistId]);
    if ($stmt->fetch()) {
        jsonResponse(['success' => false, 'message' => 'IP đã có trong danh sách đen'], 400);
    }
    
    // Cập nhật
    $stmt = $pdo->prepare("UPDATE ip_blacklist SET ip = ?, reason = ? WHERE id = ?");
    $stmt->execute([$ip, $reason, $blacklistId]);
    
    // Ghi log
    logAdminActivity($admin['id'], 'update_blacklist', "Cập nhật IP trong danh sách đen: $ip", 'blacklist', $blacklistId);
    
    jsonResponse(['success' => true, 'message' => 'Cập nhật IP thành công']);
}

function handleRemoveFromBlacklist($admin) {
    global $pdo;
    
    $blacklistId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if (!$blacklistId) {
        jsonResponse(['success' => false, 'message' => 'ID không hợp lệ'], 400);
    }
    
    // Kiểm tra blacklist tồn tại
    $stmt = $pdo->prepare("SELECT ip FROM ip_blacklist WHERE id = ?");
    $stmt->execute([$blacklistId]);
    $item = $stmt->fetch();
    if (!$item) {
        jsonResponse(['success' => false, 'message' => 'Mục không tồn tại'], 404);
    }
    
    // Xóa khỏi blacklist
    $stmt = $pdo->prepare("DELETE FROM ip_blacklist WHERE id = ?");
    $stmt->execute([$blacklistId]);
    
    // Ghi log
    logAdminActivity($admin['id'], 'remove_blacklist', "Xóa IP khỏi danh sách đen: {$item['ip']}", 'blacklist', $blacklistId);
    
    jsonResponse(['success' => true, 'message' => 'Xóa IP khỏi danh sách đen thành công']);
}

function logAdminActivity($adminId, $action, $description, $targetType = null, $targetId = null) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO admin_activities (admin_id, action, target_type, target_id, description, ip) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute([$adminId, $action, $targetType, $targetId, $description, $_SERVER['REMOTE_ADDR']]);
}
