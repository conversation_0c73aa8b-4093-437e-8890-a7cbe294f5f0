<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

function generateMathCaptcha() {
    $num1 = rand(1, 10);
    $num2 = rand(1, 10);
    $operators = ['+', '-'];
    $operator = $operators[array_rand($operators)];
    
    if ($operator === '+') {
        $result = $num1 + $num2;
        $question = "$num1 + $num2";
    } else {
        // Đảm bảo kết quả không âm
        if ($num1 < $num2) {
            $temp = $num1;
            $num1 = $num2;
            $num2 = $temp;
        }
        $result = $num1 - $num2;
        $question = "$num1 - $num2";
    }
    
    return ['question' => $question, 'answer' => $result];
}

$captcha_data = generateMathCaptcha();
$_SESSION['captcha'] = $captcha_data['answer'];

// Debug: Log session info chi tiết
error_log("DEBUG get_captcha: Question=" . $captcha_data['question'] . ", Answer=" . $captcha_data['answer'] . ", Session ID=" . session_id());
error_log("DEBUG get_captcha: Session data before save=" . print_r($_SESSION, true));

jsonResponse([
    'success' => true,
    'question' => $captcha_data['question'],
    'debug_answer' => $captcha_data['answer'], // Tạm thời để debug
    'session_id' => session_id()
]);
?>
