<?php
require_once '../config.php';

$user = requireAuth();

try {
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetAdminMessages($user);
            break;
        case 'POST':
            handleSendAdminMessage($user);
            break;
        case 'PUT':
            handleMarkAsRead($user);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['success' => false, 'message' => 'Lỗi server'], 500);
}

function handleGetAdminMessages($user) {
    global $pdo;
    
    $adminId = isset($_GET['admin_id']) ? (int)$_GET['admin_id'] : 0;
    $lastId = isset($_GET['last_id']) ? (int)$_GET['last_id'] : 0;
    $limit = isset($_GET['limit']) ? min(50, max(1, (int)$_GET['limit'])) : 20;
    
    // Nếu user là admin, có thể xem tất cả cuộc trò chuyện
    if ($user['is_admin'] == 1) {
        if ($adminId) {
            // Admin xem cuộc trò chuyện với user cụ thể
            $whereClause = "WHERE (user_id = ? AND admin_id = ?) OR (user_id = ? AND admin_id = ?)";
            $params = [$adminId, $user['id'], $user['id'], $adminId];
        } else {
            // Admin xem tất cả tin nhắn của mình
            $whereClause = "WHERE admin_id = ?";
            $params = [$user['id']];
        }
    } else {
        // User thường chỉ xem tin nhắn với admin cụ thể
        if (!$adminId) {
            jsonResponse(['success' => false, 'message' => 'Cần chỉ định admin'], 400);
        }
        $whereClause = "WHERE (user_id = ? AND admin_id = ?) OR (user_id = ? AND admin_id = ?)";
        $params = [$user['id'], $adminId, $adminId, $user['id']];
    }
    
    if ($lastId > 0) {
        $whereClause .= " AND am.id > ?";
        $params[] = $lastId;
    }
    
    // Lấy tin nhắn
    $stmt = $pdo->prepare("
        SELECT
            am.id, am.user_id, am.admin_id, am.sender_id, am.content,
            am.img, am.is_read, am.created_at,
            u.username as user_username, u.nickname as user_nickname, u.avatar as user_avatar,
            a.username as admin_username, a.nickname as admin_nickname, a.avatar as admin_avatar,
            s.username as sender_username, s.nickname as sender_nickname, s.avatar as sender_avatar
        FROM admin_messages am
        LEFT JOIN users u ON am.user_id = u.id
        LEFT JOIN users a ON am.admin_id = a.id
        LEFT JOIN users s ON am.sender_id = s.id
        $whereClause
        ORDER BY am.created_at ASC
        LIMIT ?
    ");
    $executeParams = array_merge($params, [$limit]);
    $stmt->execute($executeParams);
    $messages = $stmt->fetchAll();
    
    // Format dữ liệu
    $formattedMessages = [];
    foreach ($messages as $message) {
        $formattedMessages[] = [
            'id' => (int)$message['id'],
            'user_id' => (int)$message['user_id'],
            'admin_id' => (int)$message['admin_id'],
            'sender_id' => (int)$message['sender_id'],
            'content' => $message['content'],
            'img' => $message['img'],
            'is_read' => (int)$message['is_read'],
            'created_at' => $message['created_at'],
            'user' => [
                'username' => $message['user_username'],
                'nickname' => $message['user_nickname'] ?: $message['user_username'],
                'avatar' => $message['user_avatar']
            ],
            'admin' => [
                'username' => $message['admin_username'],
                'nickname' => $message['admin_nickname'] ?: $message['admin_username'],
                'avatar' => $message['admin_avatar']
            ],
            'sender' => [
                'username' => $message['sender_username'],
                'nickname' => $message['sender_nickname'] ?: $message['sender_username'],
                'avatar' => $message['sender_avatar']
            ]
        ];
    }
    
    jsonResponse([
        'success' => true,
        'message' => 'Lấy tin nhắn thành công',
        'messages' => $formattedMessages
    ]);
}

function handleSendAdminMessage($user) {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $content = trim($input['content'] ?? '');
    $img = trim($input['img'] ?? '');
    $adminId = isset($input['admin_id']) ? (int)$input['admin_id'] : 0;
    $userId = isset($input['user_id']) ? (int)$input['user_id'] : 0;
    
    // Validate
    if (empty($content) && empty($img)) {
        jsonResponse(['success' => false, 'message' => 'Nội dung tin nhắn không được để trống'], 400);
    }
    
    if (strlen($content) > 1000) {
        jsonResponse(['success' => false, 'message' => 'Nội dung tin nhắn quá dài'], 400);
    }
    
    // Xác định user_id và admin_id
    if ($user['is_admin'] == 1) {
        // Admin gửi tin nhắn
        if (!$userId) {
            jsonResponse(['success' => false, 'message' => 'Cần chỉ định người dùng'], 400);
        }
        $finalUserId = $userId;
        $finalAdminId = $user['id'];
    } else {
        // User thường gửi tin nhắn cho admin
        if (!$adminId) {
            jsonResponse(['success' => false, 'message' => 'Cần chỉ định admin'], 400);
        }
        
        // Kiểm tra admin tồn tại
        $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ? AND is_admin = 1");
        $stmt->execute([$adminId]);
        if (!$stmt->fetch()) {
            jsonResponse(['success' => false, 'message' => 'Admin không tồn tại'], 400);
        }
        
        $finalUserId = $user['id'];
        $finalAdminId = $adminId;
    }
    
    // Lưu tin nhắn
    $stmt = $pdo->prepare("
        INSERT INTO admin_messages (user_id, admin_id, sender_id, content, img) 
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->execute([$finalUserId, $finalAdminId, $user['id'], $content, $img]);
    
    $messageId = $pdo->lastInsertId();
    
    jsonResponse([
        'success' => true,
        'message' => 'Gửi tin nhắn thành công',
        'message_id' => (int)$messageId
    ]);
}

function handleMarkAsRead($user) {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $messageIds = $input['message_ids'] ?? [];
    
    if (empty($messageIds) || !is_array($messageIds)) {
        jsonResponse(['success' => false, 'message' => 'Danh sách tin nhắn không hợp lệ'], 400);
    }
    
    // Chỉ admin mới có thể đánh dấu đã đọc
    if ($user['is_admin'] != 1) {
        jsonResponse(['success' => false, 'message' => 'Không có quyền thực hiện'], 403);
    }
    
    $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
    $stmt = $pdo->prepare("
        UPDATE admin_messages 
        SET is_read = 1 
        WHERE id IN ($placeholders) AND admin_id = ?
    ");
    $stmt->execute([...$messageIds, $user['id']]);
    
    jsonResponse(['success' => true, 'message' => 'Đánh dấu đã đọc thành công']);
}
