<?php
require_once 'config.php';

try {
    // Thêm cột token vào bảng users
    $pdo->exec("ALTER TABLE users ADD COLUMN token VARCHAR(255) NULL");
    echo "✅ Đã thêm cột token vào bảng users\n";
} catch (PDOException $e) {
    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
        echo "ℹ️ Cột token đã tồn tại\n";
    } else {
        echo "❌ Lỗi: " . $e->getMessage() . "\n";
    }
}

try {
    // Thêm index cho token
    $pdo->exec("CREATE INDEX idx_users_token ON users(token)");
    echo "✅ Đã tạo index cho cột token\n";
} catch (PDOException $e) {
    if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
        echo "ℹ️ Index token đã tồn tại\n";
    } else {
        echo "❌ Lỗi tạo index: " . $e->getMessage() . "\n";
    }
}

echo "\n✅ Cập nhật database hoàn tất!";
?>
