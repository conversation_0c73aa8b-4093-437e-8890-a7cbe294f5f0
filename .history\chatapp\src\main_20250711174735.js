import { createApp } from 'vue'
import { createP<PERSON> } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import naive from 'naive-ui'
import App from './App.vue'
import routes from './router/index.js'
import { useAuthStore } from './stores/auth.js'
import './style.css'

const router = createRouter({
  history: createWebHistory(),
  routes
})

const pinia = createPinia()

const app = createApp(App)
app.use(pinia)
app.use(router)
app.use(naive)

// Router guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  console.log('=== ROUTER GUARD DEBUG ===')
  console.log('Going to:', to.path)
  console.log('Token from localStorage:', localStorage.getItem('token'))
  console.log('User from localStorage:', localStorage.getItem('user'))
  console.log('Auth store token:', authStore.token)
  console.log('Auth store user:', authStore.user)
  console.log('Is authenticated:', authStore.isAuthenticated)

  // Khôi phục user từ localStorage nếu có token
  if (authStore.token && !authStore.user) {
    console.log('Fetching user data...')
    try {
      const result = await authStore.fetchUser()
      console.log('Fetch user result:', result)
    } catch (error) {
      console.error('Failed to fetch user:', error)
      authStore.logout()
    }
  }

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)

  console.log('Route requires auth:', requiresAuth)
  console.log('Route requires guest:', requiresGuest)
  console.log('Route requires admin:', requiresAdmin)
  console.log('Final auth check:', authStore.isAuthenticated)

  if (requiresAuth && !authStore.isAuthenticated) {
    console.log('Redirecting to login - not authenticated')
    next('/login')
  } else if (requiresGuest && authStore.isAuthenticated) {
    console.log('Redirecting to chat - already authenticated')
    next('/chat')
  } else if (requiresAdmin && !authStore.isAdmin) {
    console.log('Redirecting to chat - not admin')
    next('/chat')
  } else {
    console.log('Allowing navigation')
    next()
  }
  console.log('=== END ROUTER GUARD ===')
})

app.mount('#app')
