import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '../utils/api'

export const useAuthStore = defineStore('auth', () => {
  // Initialize state from localStorage
  const user = ref(JSON.parse(localStorage.getItem('user') || 'null'))
  const token = ref(localStorage.getItem('token'))

  const isAuthenticated = computed(() => {
    return !!token.value
  })
  
  const isAdmin = computed(() => user.value?.is_admin === 1 || user.value?.is_admin === '1')
  
  const login = async (credentials) => {
    try {
      const response = await authAPI.login(credentials);
      if (response.data.success) {
        token.value = response.data.token;
        user.value = response.data.user;
        localStorage.setItem('token', token.value);
        localStorage.setItem('user', JSON.stringify(response.data.user));
        return { success: true, user: response.data.user };
      }
      return { success: false, message: response.data.message };
    } catch (error) {
      if (error.response && error.response.status === 401) {
        return { success: false, message: 'Tên đăng nhập hoặc mật khẩu không đúng.' };
      }
      return { success: false, message: 'Lỗi không xác định, vui lòng thử lại.' };
    }
  };

  const register = async (userData) => {
    try {
      const response = await authAPI.register(userData)
      return response.data
    } catch (error) {
      if (error.response && error.response.data && error.response.data.message) {
        return { success: false, message: error.response.data.message }
      }
      return { success: false, message: 'Đăng ký thất bại' }
    }
  }
  
  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  const fetchUser = async () => {
    try {
      const response = await authAPI.getUser()
      if (response.data.success) {
        user.value = response.data.user
        localStorage.setItem('user', JSON.stringify(response.data.user))
        return { success: true }
      }
      return { success: false }
    } catch (error) {
      logout()
      return { success: false }
    }
  }

  // Initialize auth state
  const initAuth = async () => {
    if (token.value && !user.value) {
      await fetchUser()
    }
  }
  
  return {
    user,
    token,
    isAuthenticated,
    isAdmin,
    login,
    register,
    logout,
    fetchUser,
    initAuth
  }
})
