<template>
  <div class="admin-blacklist">
    <n-card title="Quản lý danh sách đen IP">
      <template #header-extra>
        <n-space>
          <n-input
            v-model:value="searchQuery"
            placeholder="<PERSON><PERSON><PERSON> kiếm IP..."
            clearable
            style="width: 200px;"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
          
          <n-button type="primary" @click="showAddIPModal = true">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                </svg>
              </n-icon>
            </template>
            Thêm IP
          </n-button>
        </n-space>
      </template>
      
      <n-data-table
        :columns="columns"
        :data="filteredBlacklist"
        :pagination="pagination"
        :loading="loading"
        size="small"
      />
    </n-card>
    
    <n-modal v-model:show="showAddIPModal" preset="dialog" title="Thêm IP vào danh sách đen">
      <n-form ref="addIPFormRef" :model="newIP" :rules="addIPRules">
        <n-form-item path="ip" label="Địa chỉ IP">
          <n-input v-model:value="newIP.ip" placeholder="Nhập địa chỉ IP (VD: ***********)" />
        </n-form-item>
        
        <n-form-item path="reason" label="Lý do chặn">
          <n-input
            v-model:value="newIP.reason"
            type="textarea"
            placeholder="Nhập lý do chặn IP này..."
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showAddIPModal = false">Hủy</n-button>
          <n-button type="primary" @click="handleAddIP" :loading="addingIP">
            Thêm
          </n-button>
        </n-space>
      </template>
    </n-modal>
    
    <n-modal v-model:show="showEditIPModal" preset="dialog" title="Chỉnh sửa IP">
      <n-form ref="editIPFormRef" :model="editingIP" :rules="editIPRules">
        <n-form-item path="ip" label="Địa chỉ IP">
          <n-input v-model:value="editingIP.ip" placeholder="Nhập địa chỉ IP" />
        </n-form-item>
        
        <n-form-item path="reason" label="Lý do chặn">
          <n-input
            v-model:value="editingIP.reason"
            type="textarea"
            placeholder="Nhập lý do chặn IP này..."
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showEditIPModal = false">Hủy</n-button>
          <n-button type="primary" @click="handleEditIP" :loading="editingIPLoading">
            Cập nhật
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { NButton, NPopconfirm, NText, useMessage } from 'naive-ui'
import { adminAPI } from '../../utils/api'

const message = useMessage()

const blacklist = ref([])
const loading = ref(false)
const searchQuery = ref('')

const showAddIPModal = ref(false)
const showEditIPModal = ref(false)
const addingIP = ref(false)
const editingIPLoading = ref(false)

const addIPFormRef = ref()
const editIPFormRef = ref()

const newIP = ref({
  ip: '',
  reason: ''
})

const editingIP = ref({})

const pagination = {
  pageSize: 10
}

const filteredBlacklist = computed(() => {
  if (!searchQuery.value) return blacklist.value
  
  return blacklist.value.filter(item => 
    item.ip.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    item.reason.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const columns = [
  {
    title: 'Địa chỉ IP',
    key: 'ip',
    width: 150
  },
  {
    title: 'Lý do chặn',
    key: 'reason',
    render: (row) => h(NText, {
      style: 'max-width: 300px; word-break: break-word;'
    }, { default: () => row.reason || 'Không có lý do' })
  },
  {
    title: 'Người thêm',
    key: 'admin_username',
    width: 120,
    render: (row) => row.admin_username || 'Hệ thống'
  },
  {
    title: 'Thời gian thêm',
    key: 'created_at',
    width: 150,
    render: (row) => new Date(row.created_at).toLocaleString('vi-VN')
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 150,
    render: (row) => h('div', { style: 'display: flex; gap: 8px;' }, [
      h(NButton, {
        size: 'small',
        onClick: () => handleEditIPClick(row)
      }, { default: () => 'Sửa' }),
      
      h(NPopconfirm, {
        onPositiveClick: () => handleDeleteIP(row.id)
      }, {
        trigger: () => h(NButton, {
          size: 'small',
          type: 'error'
        }, { default: () => 'Xóa' }),
        default: () => 'Bạn có chắc muốn xóa IP này khỏi danh sách đen?'
      })
    ])
  }
]

const addIPRules = {
  ip: [
    { required: true, message: 'Vui lòng nhập địa chỉ IP', trigger: 'blur' },
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: 'Địa chỉ IP không hợp lệ',
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: 'Vui lòng nhập lý do chặn', trigger: 'blur' }
  ]
}

const editIPRules = {
  ip: [
    { required: true, message: 'Vui lòng nhập địa chỉ IP', trigger: 'blur' },
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: 'Địa chỉ IP không hợp lệ',
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: 'Vui lòng nhập lý do chặn', trigger: 'blur' }
  ]
}

const fetchBlacklist = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getBlacklist()
    if (response.data.success) {
      blacklist.value = response.data.blacklist
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách đen')
  } finally {
    loading.value = false
  }
}

const handleAddIP = async () => {
  try {
    await addIPFormRef.value?.validate()
    addingIP.value = true

    const response = await adminAPI.addToBlacklist(newIP.value)

    if (response.data.success) {
      message.success('Thêm IP vào danh sách đen thành công')
      showAddIPModal.value = false
      newIP.value = { ip: '', reason: '' }
      fetchBlacklist()
    } else {
      message.error(response.data.message || 'Thêm IP thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    addingIP.value = false
  }
}

const handleEditIPClick = (ip) => {
  editingIP.value = { ...ip }
  showEditIPModal.value = true
}

const handleEditIP = async () => {
  try {
    await editIPFormRef.value?.validate()
    editingIPLoading.value = true

    const response = await adminAPI.updateBlacklist(editingIP.value.id, editingIP.value)

    if (response.data.success) {
      message.success('Cập nhật IP thành công')
      showEditIPModal.value = false
      fetchBlacklist()
    } else {
      message.error(response.data.message || 'Cập nhật IP thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    editingIPLoading.value = false
  }
}

const handleDeleteIP = async (ipId) => {
  try {
    const response = await adminAPI.removeFromBlacklist(ipId)

    if (response.data.success) {
      message.success('Xóa IP khỏi danh sách đen thành công')
      fetchBlacklist()
    } else {
      message.error(response.data.message || 'Xóa IP thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa IP')
  }
}

onMounted(() => {
  fetchBlacklist()
})
</script>

<style scoped>
.admin-blacklist {
  max-width: 1200px;
}
</style>
