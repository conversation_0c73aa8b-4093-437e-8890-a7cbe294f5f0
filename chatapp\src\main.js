import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import naive from 'naive-ui'
import App from './App.vue'
import routes from './router/index.js'
import { useAuthStore } from './stores/auth.js'
import './style.css'

const router = createRouter({
  history: createWebHistory(),
  routes
})

const pinia = createPinia()
const app = createApp(App)

app.use(pinia)
app.use(naive)

// Initialize auth store before setting up router guards
const authStore = useAuthStore()
await authStore.initAuth()

// Router guards
router.beforeEach(async (to, from, next) => {
  if (authStore.token && !authStore.user) {
    await authStore.fetchUser()
  }
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)

  if (requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (requiresGuest && authStore.isAuthenticated) {
    next('/chat')
  } else if (requiresAdmin && !authStore.isAdmin) {
    next('/chat')
  } else {
    next()
  }
})

app.use(router)
app.mount('#app')
