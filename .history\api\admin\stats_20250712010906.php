<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

$user = requireAdmin();

try {
    // Thống kê tổng số người dùng
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_users FROM users WHERE status = 1");
    $stmt->execute();
    $totalUsers = $stmt->fetch()['total_users'];

    // Thống kê người dùng online (trong 5 phút gần đây)
    $stmt = $pdo->prepare("SELECT COUNT(*) as online_users FROM users WHERE last_seen >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) AND status = 1");
    $stmt->execute();
    $onlineUsers = $stmt->fetch()['online_users'];

    // Thống kê tin nhắn hôm nay
    $stmt = $pdo->prepare("SELECT COUNT(*) as today_messages FROM messages WHERE DATE(created_at) = CURDATE()");
    $stmt->execute();
    $todayMessages = $stmt->fetch()['today_messages'];

    // Thống kê tổng tin nhắn
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_messages FROM messages");
    $stmt->execute();
    $totalMessages = $stmt->fetch()['total_messages'];

    // Thống kê người dùng mới hôm nay
    $stmt = $pdo->prepare("SELECT COUNT(*) as new_users_today FROM users WHERE DATE(created_at) = CURDATE()");
    $stmt->execute();
    $newUsersToday = $stmt->fetch()['new_users_today'];

    // Thống kê IP bị cấm
    $stmt = $pdo->prepare("SELECT COUNT(*) as blocked_ips FROM ip_blacklist");
    $stmt->execute();
    $blockedIps = $stmt->fetch()['blocked_ips'];

    // Thống kê tin nhắn riêng với admin
    $stmt = $pdo->prepare("SELECT COUNT(*) as admin_messages FROM admin_messages WHERE DATE(created_at) = CURDATE()");
    $stmt->execute();
    $adminMessages = $stmt->fetch()['admin_messages'];

    // Thống kê tin nhắn riêng chưa đọc
    $stmt = $pdo->prepare("SELECT COUNT(*) as unread_admin_messages FROM admin_messages WHERE is_read = 0 AND admin_id = ?");
    $stmt->execute([$user['id']]);
    $unreadAdminMessages = $stmt->fetch()['unread_admin_messages'];

    $stats = [
        'totalUsers' => (int)$totalUsers,
        'onlineUsers' => (int)$onlineUsers,
        'todayMessages' => (int)$todayMessages,
        'totalMessages' => (int)$totalMessages,
        'newUsersToday' => (int)$newUsersToday,
        'blockedIps' => (int)$blockedIps,
        'adminMessages' => (int)$adminMessages,
        'unreadAdminMessages' => (int)$unreadAdminMessages
    ];

    // Ghi log hoạt động admin
    $stmt = $pdo->prepare("INSERT INTO admin_activities (admin_id, action, description, ip) VALUES (?, ?, ?, ?)");
    $stmt->execute([
        $user['id'],
        'view_stats',
        'Xem thống kê tổng quan',
        $_SERVER['REMOTE_ADDR']
    ]);

    jsonResponse(true, 'Lấy thống kê thành công', ['stats' => $stats]);

} catch (Exception $e) {
    error_log("Stats API Error: " . $e->getMessage());
    jsonResponse(false, 'Lỗi server', null, 500);
}
