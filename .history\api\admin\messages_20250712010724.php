<?php
require_once '../config.php';

$user = requireAdmin();

try {
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetMessages($user);
            break;
        case 'DELETE':
            handleDeleteMessage($user);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    error_log("Messages API Error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Lỗi server'], 500);
}

function handleGetMessages($admin) {
    global $pdo;
    
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $userId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(1, (int)$_GET['limit'])) : 50;
    $offset = ($page - 1) * $limit;
    
    $whereClause = '';
    $params = [];
    
    if ($search) {
        $whereClause = "WHERE m.content LIKE ? OR u.username LIKE ?";
        $searchParam = "%$search%";
        $params = [$searchParam, $searchParam];
    }
    
    if ($userId) {
        $userClause = $whereClause ? " AND m.user_id = ?" : "WHERE m.user_id = ?";
        $whereClause .= $userClause;
        $params[] = $userId;
    }
    
    // Lấy tổng số messages
    $countStmt = $pdo->prepare("
        SELECT COUNT(*) as total 
        FROM messages m 
        LEFT JOIN users u ON m.user_id = u.id 
        $whereClause
    ");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    // Lấy danh sách messages
    $stmt = $pdo->prepare("
        SELECT 
            m.id, m.user_id, m.content, m.img, m.ip, m.location, m.created_at,
            u.username, u.nickname, u.avatar, u.is_admin as user_is_admin
        FROM messages m
        LEFT JOIN users u ON m.user_id = u.id
        $whereClause
        ORDER BY m.created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([...$params, $limit, $offset]);
    $messages = $stmt->fetchAll();
    
    // Format dữ liệu
    $formattedMessages = [];
    foreach ($messages as $message) {
        $formattedMessages[] = [
            'id' => (int)$message['id'],
            'user_id' => (int)$message['user_id'],
            'username' => $message['username'],
            'nickname' => $message['nickname'] ?: $message['username'],
            'avatar' => $message['avatar'],
            'user_is_admin' => (int)$message['user_is_admin'],
            'content' => $message['content'],
            'img' => $message['img'],
            'ip' => $message['ip'],
            'location' => $message['location'],
            'created_at' => $message['created_at']
        ];
    }
    
    // Ghi log
    logAdminActivity($admin['id'], 'view_messages', 'Xem danh sách tin nhắn');
    
    jsonResponse([
        'success' => true,
        'message' => 'Lấy danh sách tin nhắn thành công',
        'messages' => $formattedMessages,
        'pagination' => [
            'total' => (int)$total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function handleDeleteMessage($admin) {
    global $pdo;
    
    $messageId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if (!$messageId) {
        jsonResponse(['success' => false, 'message' => 'ID tin nhắn không hợp lệ'], 400);
    }
    
    // Kiểm tra message tồn tại
    $stmt = $pdo->prepare("
        SELECT m.id, m.content, u.username 
        FROM messages m 
        LEFT JOIN users u ON m.user_id = u.id 
        WHERE m.id = ?
    ");
    $stmt->execute([$messageId]);
    $message = $stmt->fetch();
    
    if (!$message) {
        jsonResponse(['success' => false, 'message' => 'Tin nhắn không tồn tại'], 404);
    }
    
    // Xóa message
    $stmt = $pdo->prepare("DELETE FROM messages WHERE id = ?");
    $stmt->execute([$messageId]);
    
    // Ghi log
    $description = "Xóa tin nhắn của {$message['username']}: " . substr($message['content'], 0, 50);
    logAdminActivity($admin['id'], 'delete_message', $description, 'message', $messageId);
    
    jsonResponse(['success' => true, 'message' => 'Xóa tin nhắn thành công']);
}

function logAdminActivity($adminId, $action, $description, $targetType = null, $targetId = null) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO admin_activities (admin_id, action, target_type, target_id, description, ip) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute([$adminId, $action, $targetType, $targetId, $description, $_SERVER['REMOTE_ADDR']]);
}
