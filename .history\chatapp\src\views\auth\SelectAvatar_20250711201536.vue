<template>
  <div class="min-h-screen bg-slate-900 text-white flex items-center justify-center p-4">
    <div class="w-full max-w-2xl">
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold mb-2 text-purple-400">Chọ<PERSON> ảnh đại diện</h1>
        <p class="text-slate-400">H<PERSON><PERSON> chọn một nhân vật bạn yêu thích để làm avatar!</p>
      </div>
      <div class="grid grid-cols-3 gap-6">
        <div v-for="(avatar, idx) in avatars" :key="avatar.stt" class="flex flex-col items-center">
          <img
            :src="`/public/${avatar.stt}.png`"
            :alt="avatar.name"
            class="w-28 h-28 rounded-full border-4 border-transparent hover:border-purple-500 cursor-pointer transition"
            :class="{ 'border-purple-600': selected === avatar.stt }"
            @click="selectAvatar(avatar.stt)"
          />
          <span class="mt-2 text-base font-semibold text-slate-200">{{ avatar.name }}</span>
        </div>
      </div>
      <div class="mt-8 text-center">
        <button
          class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-8 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 transition disabled:opacity-50"
          :disabled="!selected || loading"
          @click="submitAvatar"
        >
          {{ loading ? 'Đang cập nhật...' : 'Xác nhận' }}
        </button>
        <div v-if="message" class="mt-4 text-green-400">{{ message }}</div>
        <div v-if="errorMessage" class="mt-4 text-red-400">{{ errorMessage }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { authAPI } from '../../utils/api'

const router = useRouter()
const authStore = useAuthStore()
const selected = ref(null)
const loading = ref(false)
const message = ref('')
const errorMessage = ref('')

const avatars = [
  { stt: 1, name: 'Tiểu Bảo' },
  { stt: 2, name: 'Hắc Phong' },
  { stt: 3, name: 'Lãnh Nguyệt' },
  { stt: 4, name: 'Bạch Hổ' },
  { stt: 5, name: 'Tuyết Nhi' },
  { stt: 6, name: 'Hỏa Vũ' },
  { stt: 7, name: 'Tiêu Dao' },
  { stt: 8, name: 'Vô Danh' },
  { stt: 9, name: 'Thanh Vân' },
  { stt: 10, name: 'Huyết Ảnh' },
  { stt: 11, name: 'Mộng Tuyết' },
  { stt: 12, name: 'Hàn Phong' },
  { stt: 13, name: 'Vô Tâm' },
  { stt: 14, name: 'Tiểu Hồ' },
  { stt: 15, name: 'Diệp Vũ' },
]

function selectAvatar(stt) {
  selected.value = stt
}

async function submitAvatar() {
  if (!selected.value) return
  loading.value = true
  errorMessage.value = ''
  message.value = ''
  try {
    // Gọi API cập nhật avatar (ví dụ: /api/auth/update_avatar.php)
    const res = await authAPI.updateAvatar({ avatar: `${selected.value}.png` })
    if (res.data.success) {
      message.value = 'Cập nhật avatar thành công!'
      // Cập nhật lại user trong store
      await authStore.fetchUser()
      setTimeout(() => {
        router.push('/login') // hoặc chuyển sang trang chat tuỳ ý
      }, 1000)
    } else {
      errorMessage.value = res.data.message || 'Cập nhật avatar thất bại!'
    }
  } catch (e) {
    errorMessage.value = 'Có lỗi xảy ra, vui lòng thử lại.'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
img {
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}
</style> 