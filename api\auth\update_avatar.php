<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

$user = requireAuth();
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['avatar'])) {
    jsonResponse(['success' => false, 'message' => 'Thiếu dữ liệu avatar'], 400);
}

$avatar = basename($input['avatar']);
$allowed = range(1, 15);
$filename = pathinfo($avatar, PATHINFO_FILENAME);
$ext = pathinfo($avatar, PATHINFO_EXTENSION);

if (!in_array($filename, array_map('strval', $allowed)) || $ext !== 'png') {
    jsonResponse(['success' => false, 'message' => 'Avatar không hợp lệ'], 400);
}

try {
    $stmt = $pdo->prepare("UPDATE users SET avatar = ? WHERE id = ?");
    $stmt->execute([$avatar, $user['id']]);
    jsonResponse(['success' => true, 'message' => 'Cập nhật avatar thành công!']);
} catch (PDOException $e) {
    jsonResponse(['success' => false, 'message' => 'Lỗi hệ thống'], 500);
} 