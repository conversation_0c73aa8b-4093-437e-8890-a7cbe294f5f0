<template>
  <div class="chat-layout">
    <div class="chat-sidebar" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <div class="sidebar-header">
        <img :src="authStore.user?.avatar || '/default-avatar.png'" class="sidebar-avatar" />
        <div class="sidebar-user-info">
          <div class="sidebar-username">{{ authStore.user?.username }}</div>
          <div class="sidebar-signature">{{ authStore.user?.signature || 'Chưa có chữ ký' }}</div>
          <div v-if="authStore.isAdmin" class="sidebar-admin-badge">Quản trị viên</div>
        </div>
        <button class="sidebar-toggle" @click="sidebarCollapsed = !sidebarCollapsed">
          <span v-if="sidebarCollapsed">☰</span>
          <span v-else>×</span>
        </button>
      </div>
      <div class="sidebar-section">
        <div class="sidebar-section-title">Ngư<PERSON>i dùng online ({{ chatStore.onlineUsers.length }})</div>
        <div class="sidebar-user-list">
          <div v-for="user in chatStore.onlineUsers" :key="user.id" class="sidebar-user-item">
            <img :src="user.avatar || '/default-avatar.png'" class="sidebar-user-avatar" />
            <div class="sidebar-user-meta">
              <span class="sidebar-user-name">{{ user.username }}</span>
              <span v-if="user.is_admin === '1'" class="sidebar-user-admin">Admin</span>
              <span class="sidebar-user-location">{{ formatLocation(user.location) }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="sidebar-section sidebar-actions">
        <button class="sidebar-action-btn" @click="$router.push('/profile')">Hồ sơ</button>
        <button v-if="authStore.isAdmin" class="sidebar-action-btn" @click="$router.push('/adminrmgihbvS6')">Quản trị</button>
        <button class="sidebar-action-btn logout" @click="handleLogout">Đăng xuất</button>
      </div>
    </div>
    <div class="chat-main">
      <div class="chat-header">
        <span class="chat-title">ROOM CHAT</span>
        <span class="chat-online-count">Online: {{ chatStore.onlineUsers.length }}</span>
      </div>
      <div class="chat-messages" ref="messagesContainer">
        <div v-for="message in chatStore.sortedMessages" :key="message.id" class="chat-message-wrapper" :class="{ 'own-message': message.user_id == authStore.user?.id }">
          <img :src="message.avatar || '/default-avatar.png'" class="chat-message-avatar" />
          <div class="chat-message-content">
            <div class="chat-message-meta">
              <span class="chat-message-username">{{ message.username }}</span>
              <span class="chat-message-location">{{ formatLocation(message.location) }}</span>
              <span class="chat-message-time">{{ formatTime(message.created_at) }}</span>
            </div>
            <div class="chat-message-text">{{ message.content }}</div>
            <div v-if="message.img" class="chat-message-img">
              <img :src="message.img" alt="Ảnh gửi kèm" class="chat-img-preview" />
            </div>
          </div>
        </div>
      </div>
      <div class="chat-input-bar">
        <button class="emoji-btn" @click="showEmojiPicker = !showEmojiPicker">😀</button>
        <input
          v-model="messageText"
          class="chat-input"
          type="text"
          placeholder="Nhập tin nhắn..."
          @keydown.enter.exact.prevent="sendMessage"
        />
        <button class="image-btn" @click="showImagePicker = true">
          <Picture theme="outline" size="28" />
        </button>
        <button class="send-btn" :disabled="sending" @click="sendMessage">
          <Send v-if="!sending" theme="outline" size="20" />
          <Loading v-else theme="outline" size="20" />
        </button>
        <div v-if="showEmojiPicker" class="emoji-picker-popup">
          <EmojiPicker :native="true" :hide-search="false" theme="dark" @select="onSelectEmoji" />
        </div>
        <div v-if="showImagePicker" class="image-picker-popup">
          <div class="image-picker-modal-bg" @click="showImagePicker = false"></div>
          <div class="image-picker-modal">
            <file-pond
              ref="pond"
              name="image"
              label-idle="Kéo & thả ảnh hoặc <span class='filepond--label-action'>Chọn ảnh</span>"
              allow-multiple="false"
              accepted-file-types="image/jpeg, image/png, image/webp"
              :server="cloudinaryServerConfig"
              @processfile="onFilePondProcess"
              @removefile="onFilePondRemove"
              style="width: 320px;"
            />
            <button class="close-modal-btn" @click="showImagePicker = false">Đóng</button>
          </div>
        </div>
        <div v-if="uploadedImgUrl" class="img-preview-bar">
          <img :src="uploadedImgUrl" class="img-preview-bar-img" />
          <button class="remove-img-btn" @click="removeUploadedImg">×</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useChatStore } from '../stores/chat'
import EmojiPicker from 'vue3-emoji-picker'
import 'vue3-emoji-picker/css'
import vueFilePond from 'vue-filepond'
import FilePondPluginImagePreview from 'filepond-plugin-image-preview'
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type'
import FilePondPluginImageExifOrientation from 'filepond-plugin-image-exif-orientation'
import 'filepond/dist/filepond.min.css'
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css'
import { Picture } from '@icon-park/vue-next'
import '@icon-park/vue-next/styles/index.css'

const FilePond = vueFilePond(FilePondPluginImagePreview, FilePondPluginFileValidateType, FilePondPluginImageExifOrientation)

const router = useRouter()
const authStore = useAuthStore()
const chatStore = useChatStore()

const sidebarCollapsed = ref(false)
const messageText = ref('')
const sending = ref(false)
const showEmojiPicker = ref(false)
const showImagePicker = ref(false)
const messagesContainer = ref()
const isDark = computed(() => window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches)

const uploadedImgUrl = ref('')
const pond = ref(null)

const cloudinaryServerConfig = computed(() => {
  const token = localStorage.getItem('token')
  return {
    process: {
      url: 'http://localhost/api/upload_image.php',
      method: 'POST',
      withCredentials: true,
      headers: {
        Authorization: token ? `Bearer ${token}` : ''
      },
      timeout: 10000,
      ondata: (formData) => {
        return formData
      },
      onload: (response) => {
        const res = JSON.parse(response)
        return res.url
      },
      onerror: (response) => {
        try {
          const res = JSON.parse(response)
          return res.message || 'Lỗi upload ảnh'
        } catch (e) {
          return 'Lỗi upload ảnh'
        }
      }
    }
  }
})

const onFilePondProcess = (error, file) => {
  if (!error && file.serverId) {
    uploadedImgUrl.value = file.serverId
    showImagePicker.value = false
  }
}
const onFilePondRemove = () => {
  uploadedImgUrl.value = ''
}
const removeUploadedImg = () => {
  uploadedImgUrl.value = ''
  if (pond.value) pond.value.removeFile()
}

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

const sendMessage = async () => {
  if (!messageText.value.trim() && !uploadedImgUrl.value) return
  sending.value = true
  // Truyền cả nội dung và link ảnh
  const result = await chatStore.sendMessage(messageText.value, uploadedImgUrl.value)
  if (result.success) {
    messageText.value = ''
    uploadedImgUrl.value = ''
    if (pond.value) pond.value.removeFile()
    showEmojiPicker.value = false
    await nextTick()
    scrollToBottom()
  }
  sending.value = false
}

const onSelectEmoji = (emoji) => {
  messageText.value += emoji.i
  showEmojiPicker.value = false
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const formatTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
}

const formatLocation = (location) => {
  if (!location) return 'Không xác định'
  try {
    if (typeof location === 'string') {
      const obj = JSON.parse(location)
      if (obj.city && obj.country) return `${obj.city}, ${obj.country}`
      if (obj.city) return obj.city
      if (obj.country) return obj.country
    } else if (typeof location === 'object' && location.city && location.country) {
      return `${location.city}, ${location.country}`
    }
  } catch (e) {}
  return location
}

watch(() => chatStore.sortedMessages.length, () => {
  nextTick(() => {
    scrollToBottom()
  })
})

onMounted(() => {
  chatStore.fetchMessages()
  chatStore.fetchOnlineUsers()
  chatStore.startPolling()
  scrollToBottom()
})
</script>

<style scoped>
.chat-layout {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #181c2f 0%, #232946 100%);
}

.chat-sidebar {
  width: 320px;
  background: #232946;
  color: #fff;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #222;
  transition: width 0.2s;
}
.sidebar-collapsed {
  width: 60px !important;
}
.sidebar-header {
  display: flex;
  align-items: center;
  padding: 24px 16px 16px 16px;
  position: relative;
}
.sidebar-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #8b5cf6;
}
.sidebar-user-info {
  margin-left: 16px;
  flex: 1;
}
.sidebar-username {
  font-size: 18px;
  font-weight: 700;
}
.sidebar-signature {
  font-size: 13px;
  color: #bdbdbd;
  margin-top: 2px;
}
.sidebar-admin-badge {
  display: inline-block;
  background: #f44336;
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 8px;
  margin-top: 4px;
}
.sidebar-toggle {
  background: none;
  border: none;
  color: #fff;
  font-size: 24px;
  position: absolute;
  right: 8px;
  top: 8px;
  cursor: pointer;
}
.sidebar-section {
  padding: 16px;
}
.sidebar-section-title {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 8px;
}
.sidebar-user-list {
  max-height: 220px;
  overflow-y: auto;
}
.sidebar-user-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.sidebar-user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 1.5px solid #8b5cf6;
}
.sidebar-user-meta {
  margin-left: 10px;
  flex: 1;
}
.sidebar-user-name {
  font-weight: 600;
  font-size: 14px;
}
.sidebar-user-admin {
  background: #f44336;
  color: #fff;
  font-size: 11px;
  border-radius: 6px;
  padding: 1px 6px;
  margin-left: 6px;
}
.sidebar-user-location {
  font-size: 12px;
  color: #bdbdbd;
  display: block;
}
.sidebar-actions {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.sidebar-action-btn {
  background: #8b5cf6;
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 10px 0;
  font-weight: 600;
  margin-bottom: 6px;
  cursor: pointer;
  transition: background 0.2s;
}
.sidebar-action-btn.logout {
  background: #f44336;
}
.sidebar-action-btn:hover {
  background: #6d28d9;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
}
.chat-header {
  height: 64px;
  background: #181c2f;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 24px;
  font-size: 20px;
  font-weight: 700;
  border-bottom: 1px solid #232946;
  justify-content: space-between;
}
.chat-title {
  font-family: 'Orbitron', monospace;
  font-size: 22px;
  letter-spacing: 2px;
}
.chat-online-count {
  font-size: 15px;
  color: #8b5cf6;
  font-weight: 600;
}
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px 12px 12px 12px;
  background: #232946;
  display: flex;
  flex-direction: column;
}
.chat-message-wrapper {
  display: flex;
  align-items: flex-end;
  margin-bottom: 18px;
}
.own-message {
  flex-direction: row-reverse;
}
.chat-message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #8b5cf6;
  margin: 0 10px;
}
.chat-message-content {
  max-width: 70vw;
  background: #181c2f;
  color: #fff;
  border-radius: 16px;
  padding: 10px 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  position: relative;
}
.own-message .chat-message-content {
  background: #8b5cf6;
  color: #fff;
}
.chat-message-meta {
  font-size: 12px;
  color: #bdbdbd;
  margin-bottom: 2px;
  display: flex;
  gap: 8px;
  align-items: center;
}
.chat-message-username {
  font-weight: 700;
  color: #fff;
}
.chat-message-location {
  color: #bdbdbd;
}
.chat-message-time {
  color: #bdbdbd;
  margin-left: auto;
}
.chat-message-text {
  font-size: 15px;
  word-break: break-word;
  white-space: pre-line;
}
.chat-input-bar {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #181c2f;
  border-top: 1px solid #232946;
  position: relative;
}
.emoji-btn {
  background: none;
  border: none;
  font-size: 24px;
  margin-right: 8px;
  cursor: pointer;
  color: #8b5cf6;
  transition: color 0.2s;
}
.emoji-btn:hover {
  color: #6d28d9;
}
.chat-input {
  flex: 1;
  border: none;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  margin-right: 8px;
  background: #232946;
  color: #fff;
  outline: none;
}
.send-btn {
  background: #8b5cf6;
  color: #fff;
  border: none;
  border-radius: 12px;
  padding: 10px 24px;
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.2s;
}
.send-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.send-btn:hover {
  background: #6d28d9;
}
.emoji-picker-popup {
  position: absolute;
  bottom: 60px;
  left: 16px;
  z-index: 1000;
  background: #232946;
  border: none;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(139,92,246,0.25), 0 2px 8px rgba(0,0,0,0.15);
  padding: 16px;
  width: 340px;
  max-width: 95vw;
  transition: box-shadow 0.2s, border 0.2s;
  animation: popupFadeIn 0.2s;
}
@keyframes popupFadeIn {
  from { opacity: 0; transform: translateY(20px);}
  to { opacity: 1; transform: translateY(0);}
}
@media (max-width: 600px) {
  .emoji-picker-popup {
    left: 2px;
    width: 98vw;
    padding: 4px;
    border-radius: 18px;
  }
}
@media (max-width: 900px) {
  .chat-sidebar {
    display: none;
  }
}
@media (max-width: 600px) {
  .chat-main {
    padding: 0;
  }
  .chat-header {
    font-size: 16px;
    padding: 0 10px;
  }
  .chat-messages {
    padding: 12px 2px 2px 2px;
  }
  .chat-message-content {
    max-width: 90vw;
    padding: 8px 10px;
  }
  .chat-input-bar {
    padding: 8px;
  }
}
.image-btn {
  background: none;
  border: none;
  margin-right: 8px;
  cursor: pointer;
  color: #8b5cf6;
  font-size: 24px;
  transition: color 0.2s;
}
.image-btn:hover {
  color: #6d28d9;
}
.image-picker-popup {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.image-picker-modal-bg {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
}
.image-picker-modal {
  position: relative;
  background: #232946;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(139,92,246,0.25), 0 2px 8px rgba(0,0,0,0.15);
  padding: 32px 24px 24px 24px;
  z-index: 10;
  min-width: 340px;
  max-width: 95vw;
}
.close-modal-btn {
  margin-top: 16px;
  background: #f44336;
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 8px 20px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}
.close-modal-btn:hover {
  background: #d32f2f;
}
.img-preview-bar {
  display: flex;
  align-items: center;
  margin-left: 8px;
  background: #232946;
  border-radius: 12px;
  padding: 4px 8px;
  margin-top: 8px;
  max-width: 220px;
}
.img-preview-bar-img {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 8px;
  margin-right: 8px;
}
.remove-img-btn {
  background: none;
  border: none;
  color: #f44336;
  font-size: 22px;
  cursor: pointer;
  margin-left: 4px;
}
.remove-img-btn:hover {
  color: #d32f2f;
}
.chat-message-img {
  margin-top: 8px;
}
.chat-img-preview {
  max-width: 220px;
  max-height: 180px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.12);
}
</style>
