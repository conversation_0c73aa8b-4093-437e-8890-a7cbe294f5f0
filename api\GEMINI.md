# Gemini Configuration for api

This file provides context to the Gemini AI assistant for the `api` directory.

## API (PHP)

This directory contains the PHP-based API endpoints.

-   **Functionality:** The API provides data to the frontend application and handles user authentication.
-   **Configuration:** The database connection is configured in `config.php`.
-   **Authentication:** The API uses a token-based authentication system.

### Coding Conventions

-   Maintain the existing PHP coding style.
-   Return JSON responses from all API endpoints.
