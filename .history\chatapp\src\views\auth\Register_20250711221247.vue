<template>
  <div class="auth-container">
    <!-- Main content -->
    <div class="relative z-10 min-h-screen flex items-center justify-center p-6">
      <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
          <h1 class="holographic-text text-5xl font-bold mb-4">ROOM CHAT</h1>
          <p class="text-white/80 text-lg font-medium">Tạo tài khoản mới</p>
          <div class="w-20 h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent mx-auto mt-4"></div>
        </div>

        <!-- Register Card -->
        <div class="liquid-glass-card neon-glow p-8">
          <div class="space-y-6">
            <!-- Message Display -->
            <div v-if="errorMessage"
                 class="text-center p-4 rounded-xl message-error">
              {{ errorMessage }}
            </div>

            <div v-if="successMessage"
                 class="text-center p-4 rounded-xl message-success">
              {{ successMessage }}
            </div>

            <Form @submit="handleRegister" :validation-schema="schema" v-slot="{ meta }" class="space-y-6">
              <!-- Username Field -->
              <div class="floating-label">
                <Field
                  name="username"
                  type="text"
                  class="liquid-input w-full"
                  placeholder=" "
                  autocomplete="username"
                />
                <label>Tên đăng nhập</label>
                <ErrorMessage name="username" class="text-red-400 text-sm mt-1" />
              </div>

              <!-- Email Field -->
              <div class="floating-label">
                <Field
                  name="email"
                  type="email"
                  class="liquid-input w-full"
                  placeholder=" "
                  autocomplete="email"
                />
                <label>Email</label>
                <ErrorMessage name="email" class="text-red-400 text-sm mt-1" />
              </div>

              <!-- Password Field -->
              <div class="floating-label">
                <Field
                  name="password"
                  :type="showPassword ? 'text' : 'password'"
                  class="liquid-input w-full"
                  placeholder=" "
                  autocomplete="new-password"
                />
                <label>Mật khẩu</label>
                <button type="button" @click="showPassword = !showPassword" class="absolute inset-y-0 right-0 pr-3 flex items-center z-10">
                  <svg class="h-5 w-5 text-white/60 hover:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path v-if="!showPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path v-if="!showPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    <path v-if="showPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                  </svg>
                </button>
                <ErrorMessage name="password" class="text-red-400 text-sm mt-1" />
              </div>

              <!-- Confirm Password Field -->
              <div class="floating-label">
                <Field
                  name="confirmPassword"
                  :type="showConfirmPassword ? 'text' : 'password'"
                  class="liquid-input w-full"
                  placeholder=" "
                  autocomplete="new-password"
                />
                <label>Xác nhận mật khẩu</label>
                <button type="button" @click="showConfirmPassword = !showConfirmPassword" class="absolute inset-y-0 right-0 pr-3 flex items-center z-10">
                  <svg class="h-5 w-5 text-white/60 hover:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path v-if="!showConfirmPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path v-if="!showConfirmPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    <path v-if="showConfirmPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                  </svg>
                </button>
                <ErrorMessage name="confirmPassword" class="text-red-400 text-sm mt-1" />
              </div>



              <!-- Register Button -->
              <button
                type="submit"
                :disabled="!meta.valid || loading"
                class="liquid-button w-full relative z-10"
              >
                <div v-if="loading" class="liquid-spinner mr-3"></div>
                {{ loading ? 'Đang tạo tài khoản...' : 'ĐĂNG KÝ' }}
              </button>
            </Form>

            <!-- Divider -->
            <div class="relative">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-white/20"></div>
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-4 bg-black/20 text-white/60 rounded-full backdrop-blur-sm">hoặc</span>
              </div>
            </div>

            <!-- Login Link -->
            <div class="text-center">
              <p class="text-white/70 mb-3">Đã có tài khoản?</p>
              <button
                @click="$router.push('/login')"
                class="liquid-button px-8 py-3 text-sm"
              >
                Đăng nhập ngay
              </button>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8">
          <p class="text-white/50 text-sm">
            © {{ new Date().getFullYear() }} tungduong88. Powered by Tung Duong Technology.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { Form, Field, ErrorMessage } from 'vee-validate'
import * as yup from 'yup'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const mathQuestion = ref('')

const schema = yup.object({
  username: yup.string().required('Vui lòng nhập tên đăng nhập').min(3, 'Tên đăng nhập phải có ít nhất 3 ký tự').max(20, 'Tên đăng nhập không được vượt quá 20 ký tự'),
  email: yup.string().required('Vui lòng nhập email').email('Email không hợp lệ'),
  password: yup.string().required('Vui lòng nhập mật khẩu').min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
  confirmPassword: yup.string()
    .oneOf([yup.ref('password'), null], 'Mật khẩu xác nhận không khớp')
    .required('Vui lòng xác nhận mật khẩu'),
  captcha: yup.number().required('Vui lòng nhập kết quả phép tính').min(0, 'Kết quả phải là số dương').max(20, 'Kết quả không hợp lệ'),
});

const refreshCaptcha = async () => {
  try {
    const response = await fetch('http://localhost/api/auth/get_captcha.php')
    const data = await response.json()
    if (data.success) {
      mathQuestion.value = data.question
      // Lưu vào localStorage để tránh mất khi re-render
      localStorage.setItem('currentCaptcha', data.question)
    }
  } catch (error) {
    mathQuestion.value = '5 + 3'
    localStorage.setItem('currentCaptcha', '5 + 3')
  }
}

onMounted(() => {
  // Khôi phục captcha từ localStorage nếu có
  const savedCaptcha = localStorage.getItem('currentCaptcha')
  if (savedCaptcha) {
    mathQuestion.value = savedCaptcha
  } else {
    refreshCaptcha()
  }
})

const handleRegister = async (values) => {
  try {
    loading.value = true
    errorMessage.value = ''
    successMessage.value = ''

    const result = await authStore.register(values)

    if (result.success) {
      successMessage.value = 'Đăng ký thành công! Đang chuyển sang bước chọn avatar...'
      // Xóa captcha đã lưu khi thành công
      localStorage.removeItem('currentCaptcha')
      setTimeout(() => {
        router.push('/select-avatar')
      }, 1000)
    } else {
      errorMessage.value = result.message || 'Đăng ký thất bại'
      // Chỉ refresh captcha nếu lỗi liên quan đến captcha
      if (result.message && result.message.includes('Captcha')) {
        localStorage.removeItem('currentCaptcha')
        refreshCaptcha()
      }
    }
  } catch (error) {
    errorMessage.value = 'Có lỗi xảy ra, vui lòng thử lại'
    refreshCaptcha()
  } finally {
    loading.value = false
  }
}
</script>
