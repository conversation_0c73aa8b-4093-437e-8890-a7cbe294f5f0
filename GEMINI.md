# Gemini Project Configuration

This file provides context to the Gemini AI assistant to help it understand the project structure, technologies used, and coding conventions.

## Project Overview

This is a web application with a PHP backend and a Vue.js frontend.

-   **Backend:** The backend is written in PHP and is located in the root directory (`/`), as well as the `api/` and `admin/` directories.
-   **Frontend:** The frontend is a Vue.js single-page application (SPA) located in the `chatapp/` directory.
-   **Database:** The database schema can be found in `database.sql`.

## General Instructions

-   When modifying PHP files, maintain the existing coding style.
-   When working on the frontend, follow the conventions established in the `chatapp` directory.
