<!DOCTYPE html>
<html>
<head>
    <title>Test Login API</title>
</head>
<body>
    <h1>Test Login API</h1>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
    async function testLogin() {
        try {
            const response = await fetch('http://localhost/api/auth/login.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: 'admin',
                    password: 'admin123'
                })
            });
            
            const data = await response.json();
            document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } catch (error) {
            document.getElementById('result').innerHTML = 'Error: ' + error.message;
        }
    }
    </script>
</body>
</html>
