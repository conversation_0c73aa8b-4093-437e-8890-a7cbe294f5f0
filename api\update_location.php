<?php
require_once 'config.php';

$user = requireAuth();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['location'])) {
    jsonResponse(['success' => false, 'message' => 'Location is required'], 400);
}

$location = trim($input['location']);

try {
    $stmt = $pdo->prepare("UPDATE users SET location = ? WHERE id = ?");
    $stmt->execute([$location, $user['id']]);
    
    jsonResponse([
        'success' => true,
        'message' => 'Cập nhật vị trí thành công'
    ]);
    
} catch (PDOException $e) {
    jsonResponse(['success' => false, 'message' => 'Lỗi khi cập nhật vị trí'], 500);
}
?>
