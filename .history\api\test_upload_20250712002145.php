<?php
$cloud_name = 'dv6hnveua';

echo "Testing Cloudinary unsigned upload...\n";
echo "Cloud name: $cloud_name\n";

$test_image_url = 'https://via.placeholder.com/300x200.jpg';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://api.cloudinary.com/v1_1/$cloud_name/image/upload");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_POSTFIELDS, [
    'file' => $test_image_url,
    'upload_preset' => 'ml_default'
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "\nHTTP Code: $http_code\n";
echo "CURL Error: $curl_error\n";
echo "Response: $response\n";

if ($http_code === 200) {
    $data = json_decode($response, true);
    if (isset($data['secure_url'])) {
        echo "\nSuccess! Image URL: " . $data['secure_url'] . "\n";
    }
} else {
    echo "\nFailed to upload\n";
}
?>
