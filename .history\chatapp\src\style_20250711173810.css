@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-gray-50 text-gray-900;
    font-family: 'Space Grotesk', sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Dark Liquid Glass Auth Background */
  .auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0f0f23 100%);
    position: relative;
    overflow: hidden;
  }

  /* Static Dark Orbs */
  .auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.05) 0%, transparent 50%);
  }

  /* Dark Liquid Glass Card */
  .liquid-glass-card {
    background: linear-gradient(135deg,
      rgba(15, 15, 35, 0.8) 0%,
      rgba(26, 26, 46, 0.6) 50%,
      rgba(15, 15, 35, 0.8) 100%
    );
    backdrop-filter: blur(20px);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 24px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.4),
      0 4px 16px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(59, 130, 246, 0.1),
      inset 0 -1px 0 rgba(139, 92, 246, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .liquid-glass-card:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.5),
      0 6px 20px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(59, 130, 246, 0.2);
  }

  /* Dark Input Fields */
  .liquid-input {
    background: rgba(15, 15, 35, 0.6);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    color: white;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.3),
      0 1px 2px rgba(59, 130, 246, 0.1);
    position: relative;
    z-index: 1;
  }

  .liquid-input:focus {
    outline: none;
    background: rgba(15, 15, 35, 0.8);
    border-color: rgba(59, 130, 246, 0.6);
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(59, 130, 246, 0.3),
      0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .liquid-input::placeholder {
    color: rgba(156, 163, 175, 0.7);
    font-weight: 400;
  }

  /* Dark Liquid Button */
  .liquid-button {
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.8) 0%,
      rgba(139, 92, 246, 0.6) 50%,
      rgba(59, 130, 246, 0.8) 100%
    );
    backdrop-filter: blur(15px);
    border: 1px solid rgba(59, 130, 246, 0.4);
    border-radius: 16px;
    color: white;
    font-weight: 600;
    font-size: 16px;
    padding: 16px 32px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow:
      0 4px 15px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .liquid-button:hover {
    transform: translateY(-2px);
    box-shadow:
      0 8px 25px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.9) 0%,
      rgba(139, 92, 246, 0.7) 50%,
      rgba(59, 130, 246, 0.9) 100%
    );
  }

  .liquid-button:active {
    transform: translateY(0px);
  }

  .liquid-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  /* Floating Labels */
  .floating-label {
    position: relative;
  }

  .floating-label label {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 2;
  }

  .floating-label input:focus + label,
  .floating-label input:not(:placeholder-shown) + label {
    top: -8px;
    left: 16px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(0, 0, 0, 0.2);
    padding: 2px 8px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
  }

  /* Dark Holographic Text */
  .holographic-text {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    background: linear-gradient(45deg,
      #3b82f6, #8b5cf6, #06d6a0, #3b82f6
    );
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* Neon Glow Effects */
  .neon-glow {
    box-shadow:
      0 0 5px rgba(255, 255, 255, 0.2),
      0 0 10px rgba(255, 255, 255, 0.2),
      0 0 15px rgba(255, 255, 255, 0.2),
      0 0 20px rgba(255, 255, 255, 0.1);
    animation: neonPulse 2s ease-in-out infinite alternate;
  }

  @keyframes neonPulse {
    from {
      box-shadow:
        0 0 5px rgba(255, 255, 255, 0.2),
        0 0 10px rgba(255, 255, 255, 0.2),
        0 0 15px rgba(255, 255, 255, 0.2),
        0 0 20px rgba(255, 255, 255, 0.1);
    }
    to {
      box-shadow:
        0 0 10px rgba(255, 255, 255, 0.4),
        0 0 20px rgba(255, 255, 255, 0.4),
        0 0 30px rgba(255, 255, 255, 0.4),
        0 0 40px rgba(255, 255, 255, 0.2);
    }
  }

  /* Loading Spinner */
  .liquid-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: liquidSpin 1s linear infinite;
  }

  @keyframes liquidSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Success/Error Messages */
  .message-success {
    background: linear-gradient(135deg,
      rgba(76, 175, 80, 0.2) 0%,
      rgba(76, 175, 80, 0.1) 100%
    );
    border: 1px solid rgba(76, 175, 80, 0.3);
    color: #4caf50;
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 12px 16px;
  }

  .message-error {
    background: linear-gradient(135deg,
      rgba(244, 67, 54, 0.2) 0%,
      rgba(244, 67, 54, 0.1) 100%
    );
    border: 1px solid rgba(244, 67, 54, 0.3);
    color: #f44336;
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 12px 16px;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .liquid-glass-card {
      margin: 20px;
      border-radius: 20px;
    }

    .liquid-input {
      padding: 14px 18px;
      font-size: 16px;
    }

    .liquid-button {
      padding: 14px 28px;
      font-size: 15px;
    }
  }
}
